/**
 * Test Clickable Statistics Cards
 * Verifies that statistics cards are clickable and filter the data correctly
 */

/**
 * Test clickable functionality of statistics cards
 */
export const testClickableStats = () => {
  try {
    console.log('🧪 Testing clickable statistics cards...');
    console.log('');

    // Check if we're on the Type of Work page
    const currentPath = window.location.pathname;
    if (!currentPath.includes('type-of-work')) {
      console.log('⚠️ Please navigate to the Type of Work page first');
      return { success: false, message: 'Not on Type of Work page' };
    }

    // Find statistics cards
    const statsCards = document.querySelectorAll('.grid .cursor-pointer');
    console.log(`🖱️ Clickable cards found: ${statsCards.length}`);

    if (statsCards.length < 3) {
      console.log('❌ Expected 3 clickable cards, found:', statsCards.length);
      return { success: false, message: 'Not enough clickable cards found' };
    }

    // Get initial table row count
    const getTableRowCount = () => {
      const tableRows = document.querySelectorAll('tbody tr');
      return tableRows.length;
    };

    const initialRowCount = getTableRowCount();
    console.log(`📊 Initial table rows: ${initialRowCount}`);

    // Test results
    const testResults = {
      totalCard: false,
      activeCard: false,
      inactiveCard: false,
      filterIndicator: false,
      clearFilter: false
    };

    // Test Total Types card (first card)
    console.log('');
    console.log('1️⃣ Testing Total Types card...');
    const totalCard = statsCards[0];
    totalCard.click();
    
    setTimeout(() => {
      const rowsAfterTotal = getTableRowCount();
      console.log(`   Rows after clicking Total: ${rowsAfterTotal}`);
      testResults.totalCard = rowsAfterTotal === initialRowCount;
      console.log(`   Total card test: ${testResults.totalCard ? '✅ PASSED' : '❌ FAILED'}`);
    }, 100);

    // Test Active Types card (second card)
    setTimeout(() => {
      console.log('');
      console.log('2️⃣ Testing Active Types card...');
      const activeCard = statsCards[1];
      activeCard.click();
      
      setTimeout(() => {
        const rowsAfterActive = getTableRowCount();
        console.log(`   Rows after clicking Active: ${rowsAfterActive}`);
        
        // Count active rows manually
        const activeRows = Array.from(document.querySelectorAll('tbody tr')).filter(row => {
          const statusCell = row.querySelector('td:nth-child(3)');
          return statusCell && statusCell.textContent.includes('Active');
        });
        
        console.log(`   Active rows in table: ${activeRows.length}`);
        testResults.activeCard = rowsAfterActive === activeRows.length && rowsAfterActive < initialRowCount;
        console.log(`   Active card test: ${testResults.activeCard ? '✅ PASSED' : '❌ FAILED'}`);
        
        // Check for filter indicator
        const filterIndicator = document.querySelector('.bg-blue-50');
        testResults.filterIndicator = !!filterIndicator;
        console.log(`   Filter indicator shown: ${testResults.filterIndicator ? '✅ PASSED' : '❌ FAILED'}`);
      }, 100);
    }, 200);

    // Test Inactive Types card (third card)
    setTimeout(() => {
      console.log('');
      console.log('3️⃣ Testing Inactive Types card...');
      const inactiveCard = statsCards[2];
      inactiveCard.click();
      
      setTimeout(() => {
        const rowsAfterInactive = getTableRowCount();
        console.log(`   Rows after clicking Inactive: ${rowsAfterInactive}`);
        
        // Count inactive rows manually
        const inactiveRows = Array.from(document.querySelectorAll('tbody tr')).filter(row => {
          const statusCell = row.querySelector('td:nth-child(3)');
          return statusCell && statusCell.textContent.includes('Inactive');
        });
        
        console.log(`   Inactive rows in table: ${inactiveRows.length}`);
        testResults.inactiveCard = rowsAfterInactive === inactiveRows.length && rowsAfterInactive < initialRowCount;
        console.log(`   Inactive card test: ${testResults.inactiveCard ? '✅ PASSED' : '❌ FAILED'}`);
      }, 100);
    }, 400);

    // Test Clear Filter button
    setTimeout(() => {
      console.log('');
      console.log('4️⃣ Testing Clear Filter button...');
      const clearButton = document.querySelector('button[onclick*="setStatusFilter"]') || 
                         Array.from(document.querySelectorAll('button')).find(btn => 
                           btn.textContent.includes('Clear Filter')
                         );
      
      if (clearButton) {
        clearButton.click();
        
        setTimeout(() => {
          const rowsAfterClear = getTableRowCount();
          console.log(`   Rows after clearing filter: ${rowsAfterClear}`);
          testResults.clearFilter = rowsAfterClear === initialRowCount;
          console.log(`   Clear filter test: ${testResults.clearFilter ? '✅ PASSED' : '❌ FAILED'}`);
          
          // Final summary
          setTimeout(() => {
            console.log('');
            console.log('📊 CLICKABLE STATS TEST SUMMARY:');
            console.log(`   Total card: ${testResults.totalCard ? '✅' : '❌'}`);
            console.log(`   Active card: ${testResults.activeCard ? '✅' : '❌'}`);
            console.log(`   Inactive card: ${testResults.inactiveCard ? '✅' : '❌'}`);
            console.log(`   Filter indicator: ${testResults.filterIndicator ? '✅' : '❌'}`);
            console.log(`   Clear filter: ${testResults.clearFilter ? '✅' : '❌'}`);
            
            const allPassed = Object.values(testResults).every(result => result);
            console.log('');
            console.log(`🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
          }, 100);
        }, 100);
      } else {
        console.log('   ❌ Clear Filter button not found');
        testResults.clearFilter = false;
      }
    }, 600);

    return {
      success: true,
      message: 'Clickable stats test initiated',
      testResults: testResults
    };

  } catch (error) {
    console.error('💥 Clickable stats test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test visual feedback of selected cards
 */
export const testVisualFeedback = () => {
  try {
    console.log('🎨 Testing visual feedback of selected cards...');
    console.log('');

    const statsCards = document.querySelectorAll('.grid .cursor-pointer');
    
    if (statsCards.length < 3) {
      console.log('❌ Not enough cards found');
      return { success: false };
    }

    const feedbackResults = {
      hoverEffects: 0,
      selectedStates: 0,
      transitions: 0
    };

    statsCards.forEach((card, index) => {
      console.log(`🔍 Checking card ${index + 1} visual feedback...`);
      
      // Check for hover classes
      const hasHoverEffects = card.classList.contains('hover:shadow-md') && 
                             card.classList.contains('hover:scale-105');
      if (hasHoverEffects) {
        feedbackResults.hoverEffects++;
        console.log(`   ✅ Has hover effects`);
      } else {
        console.log(`   ❌ Missing hover effects`);
      }
      
      // Check for transition classes
      const hasTransitions = card.classList.contains('transition-all');
      if (hasTransitions) {
        feedbackResults.transitions++;
        console.log(`   ✅ Has transitions`);
      } else {
        console.log(`   ❌ Missing transitions`);
      }
      
      // Check for selected state indicators
      const hasSelectedState = card.classList.contains('ring-2') || 
                              card.querySelector('.text-xs');
      if (hasSelectedState) {
        feedbackResults.selectedStates++;
        console.log(`   ✅ Has selected state indicators`);
      } else {
        console.log(`   ⚠️ No selected state visible (may be normal if not selected)`);
      }
    });

    console.log('');
    console.log('📊 Visual Feedback Summary:');
    console.log(`   Cards with hover effects: ${feedbackResults.hoverEffects}/3`);
    console.log(`   Cards with transitions: ${feedbackResults.transitions}/3`);
    console.log(`   Cards with selected states: ${feedbackResults.selectedStates}/3`);

    const visualFeedbackGood = feedbackResults.hoverEffects === 3 && 
                              feedbackResults.transitions === 3;

    console.log('');
    console.log(`🎨 Visual Feedback Result: ${visualFeedbackGood ? '✅ EXCELLENT' : '⚠️ NEEDS IMPROVEMENT'}`);

    return {
      success: true,
      feedback: feedbackResults,
      visualFeedbackGood: visualFeedbackGood
    };

  } catch (error) {
    console.error('💥 Visual feedback test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test filter persistence and state management
 */
export const testFilterPersistence = () => {
  try {
    console.log('💾 Testing filter persistence and state management...');
    console.log('');

    const statsCards = document.querySelectorAll('.grid .cursor-pointer');
    
    if (statsCards.length < 3) {
      console.log('❌ Not enough cards found');
      return { success: false };
    }

    // Test sequence: Total -> Active -> Inactive -> Total
    const testSequence = [
      { card: 0, name: 'Total', expected: 'all' },
      { card: 1, name: 'Active', expected: 'active' },
      { card: 2, name: 'Inactive', expected: 'inactive' },
      { card: 0, name: 'Total', expected: 'all' }
    ];

    let sequenceIndex = 0;

    const runNextTest = () => {
      if (sequenceIndex >= testSequence.length) {
        console.log('');
        console.log('✅ Filter persistence test completed successfully!');
        return;
      }

      const test = testSequence[sequenceIndex];
      console.log(`${sequenceIndex + 1}️⃣ Clicking ${test.name} card...`);
      
      statsCards[test.card].click();
      
      setTimeout(() => {
        // Check if the correct filter is applied by looking at table title or filter indicator
        const tableTitle = document.querySelector('.card h2, .card .text-lg');
        const filterIndicator = document.querySelector('.bg-blue-50');
        
        let filterApplied = false;
        
        if (test.expected === 'all') {
          filterApplied = !filterIndicator; // No filter indicator for 'all'
        } else {
          filterApplied = !!filterIndicator && 
                         filterIndicator.textContent.includes(test.expected);
        }
        
        console.log(`   Filter applied correctly: ${filterApplied ? '✅' : '❌'}`);
        
        sequenceIndex++;
        setTimeout(runNextTest, 200);
      }, 100);
    };

    runNextTest();

    return {
      success: true,
      message: 'Filter persistence test initiated'
    };

  } catch (error) {
    console.error('💥 Filter persistence test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Run all clickable statistics tests
 */
export const runAllClickableTests = () => {
  console.log('🚀 RUNNING ALL CLICKABLE STATISTICS TESTS...');
  console.log('');

  // Test 1: Basic clickable functionality
  console.log('🔄 Starting clickable functionality test...');
  const clickableTest = testClickableStats();
  
  // Test 2: Visual feedback
  setTimeout(() => {
    console.log('');
    console.log('🔄 Starting visual feedback test...');
    const visualTest = testVisualFeedback();
  }, 1000);
  
  // Test 3: Filter persistence
  setTimeout(() => {
    console.log('');
    console.log('🔄 Starting filter persistence test...');
    const persistenceTest = testFilterPersistence();
  }, 2000);

  console.log('');
  console.log('📋 Test Instructions:');
  console.log('1. Watch the console output for test results');
  console.log('2. Observe the table filtering as cards are clicked');
  console.log('3. Check for visual feedback (hover effects, selected states)');
  console.log('4. Verify filter indicator appears/disappears correctly');

  return {
    clickable: clickableTest,
    message: 'All clickable tests initiated'
  };
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.testClickableStats = testClickableStats;
  window.testVisualFeedback = testVisualFeedback;
  window.testFilterPersistence = testFilterPersistence;
  window.runAllClickableTests = runAllClickableTests;
  
  console.log('🖱️ Clickable Statistics Test Functions Available:');
  console.log('- window.testClickableStats() - Test card click functionality');
  console.log('- window.testVisualFeedback() - Test visual feedback');
  console.log('- window.testFilterPersistence() - Test filter state management');
  console.log('- window.runAllClickableTests() - Run all tests');
}
