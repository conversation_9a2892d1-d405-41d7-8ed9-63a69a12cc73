import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { isAuthenticated } from '../../services/enhancedAuthService';

const AuthGuard = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Global authentication guard
    const handleAuthCheck = () => {
      const isPublicRoute = location.pathname === '/login' || 
                           location.pathname.startsWith('/test-');
      
      if (!isPublicRoute && !isAuthenticated()) {
        console.log('🔒 AuthGuard: Unauthorized access attempt, redirecting to login');
        navigate('/login', { replace: true });
      }
    };

    // Check auth on route change
    handleAuthCheck();

    // Add global navigation guard
    const handleBeforeUnload = (event) => {
      // Clear any cached data on page unload
      if (!isAuthenticated()) {
        localStorage.clear();
        sessionStorage.clear();
      }
    };

    const handleVisibilityChange = () => {
      // Check auth when page becomes visible again
      if (document.visibilityState === 'visible') {
        const isPublicRoute = location.pathname === '/login' || 
                             location.pathname.startsWith('/test-');
        
        if (!isPublicRoute && !isAuthenticated()) {
          console.log('🔒 AuthGuard: Session expired, redirecting to login');
          navigate('/login', { replace: true });
        }
      }
    };

    // Add event listeners
    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [navigate, location.pathname]);

  return children;
};

export default AuthGuard;
