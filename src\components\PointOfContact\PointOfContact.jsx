import React, { useState, useEffect } from 'react';
import { PlusIcon, TrashIcon, UserIcon } from '@heroicons/react/24/outline';
import { getActiveAreaOfExpertise } from '../../services/areaOfExpertiseService';
import { validateEmail, validatePhone } from '../../utils/validation';
import MultiSelect from '../MultiSelect/MultiSelect';

const PointOfContact = ({ 
  value = [], 
  onChange, 
  required = false, 
  className = "",
  disabled = false 
}) => {
  const [areaOfExpertiseOptions, setAreaOfExpertiseOptions] = useState([]);
  const [loadingAreas, setLoadingAreas] = useState(false);
  const [errors, setErrors] = useState({});

  // Load area of expertise options
  useEffect(() => {
    loadAreaOfExpertise();
  }, []);

  const loadAreaOfExpertise = async () => {
    try {
      setLoadingAreas(true);
      const areas = await getActiveAreaOfExpertise();
      console.log('🎯 PointOfContact loaded areas:', areas.length, 'items');
      setAreaOfExpertiseOptions(areas);
    } catch (error) {
      console.error('Error loading area of expertise:', error);
      // Fallback to default options if service fails
      const fallbackAreas = [
        { id: 1, name: 'Patent Law', description: 'Patent filing and prosecution' },
        { id: 2, name: 'Trademark Law', description: 'Trademark registration and protection' },
        { id: 3, name: 'Copyright Law', description: 'Copyright registration and enforcement' },
        { id: 4, name: 'IP Litigation', description: 'Intellectual property litigation' },
        { id: 5, name: 'Technical Writing', description: 'Patent drafting and documentation' },
        { id: 6, name: 'Prior Art Search', description: 'Patent search and analysis' },
        { id: 7, name: 'Legal Consulting', description: 'General IP consulting' }
      ];
      setAreaOfExpertiseOptions(fallbackAreas);
      console.log('Using fallback area of expertise options');
    } finally {
      setLoadingAreas(false);
    }
  };

  // Initialize with one empty contact if none exist and migrate old data format
  useEffect(() => {
    if (value.length === 0) {
      addContact();
    } else {
      // Migrate old single-value areaOfExpertise to array format
      const migratedContacts = value.map(contact => ({
        ...contact,
        areaOfExpertise: Array.isArray(contact.areaOfExpertise)
          ? contact.areaOfExpertise
          : contact.areaOfExpertise
            ? [{ id: contact.areaOfExpertise, name: getAreaNameById(contact.areaOfExpertise) }]
            : []
      }));

      // Only update if migration is needed
      const needsMigration = value.some(contact =>
        !Array.isArray(contact.areaOfExpertise) && contact.areaOfExpertise
      );

      if (needsMigration) {
        onChange(migratedContacts);
      }
    }
  }, [value.length]); // Only run when length changes to avoid infinite loops

  // Helper function to get area name by ID
  const getAreaNameById = (areaId) => {
    const area = areaOfExpertiseOptions.find(a => a.id == areaId);
    return area ? area.name : `Area ${areaId}`;
  };

  const createEmptyContact = () => ({
    id: Date.now() + Math.random(), // Temporary ID for React keys
    name: '',
    phone: '',
    email: '',
    areaOfExpertise: [] // Changed to array for multi-select
  });

  const addContact = () => {
    const newContact = createEmptyContact();
    const updatedContacts = [...value, newContact];
    onChange(updatedContacts);
  };

  const removeContact = (index) => {
    if (value.length <= 1) {
      // Don't allow removing the last contact, just clear it
      const clearedContact = createEmptyContact();
      onChange([clearedContact]);
    } else {
      const updatedContacts = value.filter((_, i) => i !== index);
      onChange(updatedContacts);
    }
    
    // Clear errors for removed contact
    const newErrors = { ...errors };
    Object.keys(newErrors).forEach(key => {
      if (key.startsWith(`${index}_`)) {
        delete newErrors[key];
      }
    });
    setErrors(newErrors);
  };

  const updateContact = (index, field, newValue) => {
    const updatedContacts = value.map((contact, i) => 
      i === index ? { ...contact, [field]: newValue } : contact
    );
    onChange(updatedContacts);

    // Clear error for this field
    const errorKey = `${index}_${field}`;
    if (errors[errorKey]) {
      const newErrors = { ...errors };
      delete newErrors[errorKey];
      setErrors(newErrors);
    }
  };

  const validateContacts = () => {
    const newErrors = {};
    let hasErrors = false;

    value.forEach((contact, index) => {
      // Validate name
      if (!contact.name.trim()) {
        newErrors[`${index}_name`] = 'Name is required';
        hasErrors = true;
      } else if (contact.name.trim().length < 2) {
        newErrors[`${index}_name`] = 'Name must be at least 2 characters';
        hasErrors = true;
      }

      // Validate phone
      if (!contact.phone.trim()) {
        newErrors[`${index}_phone`] = 'Phone number is required';
        hasErrors = true;
      } else {
        const phoneValidation = validatePhone(contact.phone, 'IN');
        if (!phoneValidation.isValid) {
          newErrors[`${index}_phone`] = phoneValidation.errors[0];
          hasErrors = true;
        }
      }

      // Validate email
      if (!contact.email.trim()) {
        newErrors[`${index}_email`] = 'Email is required';
        hasErrors = true;
      } else {
        const emailValidation = validateEmail(contact.email);
        if (!emailValidation.isValid) {
          newErrors[`${index}_email`] = emailValidation.errors[0];
          hasErrors = true;
        }
      }

      // Validate area of expertise
      if (!contact.areaOfExpertise || !Array.isArray(contact.areaOfExpertise) || contact.areaOfExpertise.length === 0) {
        newErrors[`${index}_areaOfExpertise`] = 'At least one area of expertise is required';
        hasErrors = true;
      }
    });

    setErrors(newErrors);
    return !hasErrors;
  };

  // Expose validation function to parent
  useEffect(() => {
    if (onChange.validate) {
      onChange.validate = validateContacts;
    }
  }, [value]);



  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Point of Contact {required && <span className="text-red-500">*</span>}
          </label>
          <p className="text-xs text-gray-500 mt-1">
            Add contact persons for this vendor (in case of not an individual)
          </p>
        </div>
        <button
          type="button"
          onClick={addContact}
          disabled={disabled}
          className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          <PlusIcon className="w-4 h-4 mr-1" />
          Add Contact
        </button>
      </div>

      {/* Contacts List */}
      <div className="space-y-4">
        {value.map((contact, index) => (
          <div key={contact.id || index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
            {/* Contact Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <UserIcon className="w-5 h-5 text-gray-400" />
                <h4 className="text-sm font-medium text-gray-900">
                  Contact {index + 1}
                  {contact.name && <span className="text-gray-600"> - {contact.name}</span>}
                </h4>
              </div>
              {value.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeContact(index)}
                  disabled={disabled}
                  className="p-1 text-red-600 hover:bg-red-50 rounded-md disabled:opacity-50"
                  title="Remove contact"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              )}
            </div>

            {/* Contact Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Name */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Name *
                </label>
                <input
                  type="text"
                  value={contact.name}
                  onChange={(e) => updateContact(index, 'name', e.target.value)}
                  disabled={disabled}
                  className={`w-full px-3 py-2 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 ${
                    errors[`${index}_name`] ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Contact person name"
                />
                {errors[`${index}_name`] && (
                  <p className="text-red-600 text-xs mt-1">{errors[`${index}_name`]}</p>
                )}
              </div>

              {/* Phone */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  value={contact.phone}
                  onChange={(e) => updateContact(index, 'phone', e.target.value)}
                  disabled={disabled}
                  className={`w-full px-3 py-2 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 ${
                    errors[`${index}_phone`] ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="+91 9876543210"
                />
                {errors[`${index}_phone`] && (
                  <p className="text-red-600 text-xs mt-1">{errors[`${index}_phone`]}</p>
                )}
              </div>

              {/* Email */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Email ID *
                </label>
                <input
                  type="email"
                  value={contact.email}
                  onChange={(e) => updateContact(index, 'email', e.target.value)}
                  disabled={disabled}
                  className={`w-full px-3 py-2 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 ${
                    errors[`${index}_email`] ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="<EMAIL>"
                />
                {errors[`${index}_email`] && (
                  <p className="text-red-600 text-xs mt-1">{errors[`${index}_email`]}</p>
                )}
              </div>

              {/* Area of Expertise */}
              <div>
                <MultiSelect
                  label="Area of Expertise *"
                  options={areaOfExpertiseOptions}
                  value={contact.areaOfExpertise || []}
                  onChange={(selectedAreas) => updateContact(index, 'areaOfExpertise', selectedAreas)}
                  placeholder={loadingAreas ? 'Loading...' : 'Select areas of expertise'}
                  disabled={disabled || loadingAreas}
                  required={true}
                  searchable={true}
                  showSelectAll={true}
                  maxHeight="200px"
                  className="text-sm"
                  error={errors[`${index}_areaOfExpertise`]}
                />
                {areaOfExpertiseOptions.length === 0 && !loadingAreas && (
                  <p className="text-xs text-gray-500 mt-1">
                    No areas available. Please add areas from the Area of Expertise page.
                  </p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Summary */}
      {value.length > 0 && (
        <div className="text-xs text-gray-500 bg-blue-50 p-3 rounded-md">
          <strong>Summary:</strong> {value.length} contact{value.length !== 1 ? 's' : ''} added
          {value.some(c => c.name) && (
            <span> - {value.filter(c => c.name).map(c => c.name).join(', ')}</span>
          )}
        </div>
      )}
    </div>
  );
};

export default PointOfContact;
