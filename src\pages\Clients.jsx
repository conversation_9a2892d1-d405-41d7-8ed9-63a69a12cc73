import { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { PlusIcon, EyeIcon, PencilIcon, TrashIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import DataTable from '../components/DataTable/DataTable';
import FileUpload from '../components/FileUpload/FileUpload';
import S3FileUpload from '../components/FileUpload/S3FileUpload';
import { getAllStates, getCitiesByStateName, getAllCountries } from '../services/locationService';
import LocationSelector from '../components/LocationSelector/LocationSelector';
import MultiSelect from '../components/MultiSelect/MultiSelect';
import { getActiveCompanyTypes } from '../services/companyTypeService';
import { validateEmails, validatePhones, validateCompanyName, validateEmail, validatePhone, getPhonePlaceholder } from '../utils/validation';

// Country-State-City data
const countryStateCity = {
  'India': {
    'Maharashtra': ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad'],
    'Karnataka': ['Bangalore', 'Mysore', 'Hubli', 'Mangalore', 'Belgaum'],
    'Tamil Nadu': ['Chennai', 'Coimbatore', 'Madurai', 'Tiruchirappalli', 'Salem'],
    'Gujarat': ['Ahmedabad', 'Surat', 'Vadodara', 'Rajkot', 'Bhavnagar'],
    'Delhi': ['New Delhi', 'Central Delhi', 'North Delhi', 'South Delhi', 'East Delhi']
  },
  'United States': {
    'California': ['Los Angeles', 'San Francisco', 'San Diego', 'Sacramento', 'San Jose'],
    'New York': ['New York City', 'Buffalo', 'Rochester', 'Syracuse', 'Albany'],
    'Texas': ['Houston', 'Dallas', 'Austin', 'San Antonio', 'Fort Worth'],
    'Florida': ['Miami', 'Orlando', 'Tampa', 'Jacksonville', 'Tallahassee']
  },
  'United Kingdom': {
    'England': ['London', 'Manchester', 'Birmingham', 'Liverpool', 'Leeds'],
    'Scotland': ['Edinburgh', 'Glasgow', 'Aberdeen', 'Dundee', 'Stirling'],
    'Wales': ['Cardiff', 'Swansea', 'Newport', 'Bangor', 'St. Davids']
  }
};

const Clients = () => {
  const navigate = useNavigate();
  const [showAddForm, setShowAddForm] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all'); // 'all', 'active', 'inactive'
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [formData, setFormData] = useState({
    companyType: '',
    companyName: '',
    emails: [''],
    phones: [''],
    address: '',
    country: '',
    state: '',
    city: '',
    username: '',
    gstNumber: '',
    dpiitRegistered: '',
    validTill: '',
    website: '',
    description: '',
    creditLimit: '',
    paymentTerms: ''
  });
  const [uploadedFiles, setUploadedFiles] = useState({
    dpiitCertificate: [],
    tdsFile: [],
    gstFile: [],
    ndaFile: [],
    agreementFile: [],
    quotationFile: [],
    panCardFile: [],
    udhyamRegistrationFile: [],
    othersFile: []
  });
  const [tempClientId] = useState(() => `temp-client-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  const [availableStates, setAvailableStates] = useState([]);
  const [availableCities, setAvailableCities] = useState([]);
  const [companyTypeOptions, setCompanyTypeOptions] = useState([]);
  const [loadingStates, setLoadingStates] = useState(false);
  const [loadingCities, setLoadingCities] = useState(false);
  const [locationData, setLocationData] = useState({});

  // Client data from database only
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [validationErrors, setValidationErrors] = useState({});

  // Load clients from database
  const loadClients = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      console.log('🔄 Loading clients from database...');
      // Import and use client service
      const { getAllClients } = await import('../services/clientService');
      const dbClients = await getAllClients();
      console.log('✅ Clients loaded:', dbClients);

      if (dbClients && dbClients.length > 0) {
        console.log('📊 First client structure:', dbClients[0]);
      }

      setClients(dbClients || []);
    } catch (err) {
      console.error('❌ Error loading clients:', err);
      setClients([]);
    } finally {
      if (showLoading) setLoading(false);
    }
  };

  useEffect(() => {
    loadClients();
    loadStates();
    loadCompanyTypes();
  }, []);

  // Calculate statistics
  const totalClients = clients.length;
  const activeClients = clients.filter(client => client.status === 'Active').length;
  const inactiveClients = clients.filter(client => client.status === 'Inactive').length;

  // Filter clients based on status
  const filteredClients = useMemo(() => {
    switch (filterStatus) {
      case 'active':
        return clients.filter(client => client.status === 'Active');
      case 'inactive':
        return clients.filter(client => client.status === 'Inactive');
      default:
        return clients;
    }
  }, [clients, filterStatus]);

  // Load company type options
  const loadCompanyTypes = async () => {
    try {
      console.log('🔄 Loading company types...');
      const companyTypes = await getActiveCompanyTypes();
      setCompanyTypeOptions(companyTypes);
      console.log(`✅ Loaded ${companyTypes.length} company types`);
    } catch (error) {
      console.error('❌ Error loading company types:', error);
      // Fallback company types to prevent form from breaking
      setCompanyTypeOptions([
        { id: 1, name: 'Private Limited Company' },
        { id: 2, name: 'Public Limited Company' },
        { id: 3, name: 'Partnership Firm' },
        { id: 4, name: 'Sole Proprietorship' },
        { id: 5, name: 'LLP (Limited Liability Partnership)' },
        { id: 6, name: 'Individual' }
      ]);
    }
  };

  // Load states from local JSON
  const loadStates = async () => {
    try {
      setLoadingStates(true);
      console.log('🔄 Loading states from local JSON...');
      const states = await getAllStates();
      setAvailableStates(states);
      console.log(`✅ Loaded ${states.length} states`);
    } catch (error) {
      console.error('❌ Error loading states:', error);
    } finally {
      setLoadingStates(false);
    }
  };

  // Load cities for selected state
  const loadCities = async (stateName) => {
    try {
      setLoadingCities(true);
      console.log(`🔄 Loading cities for state ${stateName}...`);
      const cities = await getCitiesByStateName(stateName);
      setAvailableCities(cities);
      console.log(`✅ Loaded ${cities.length} cities`);
    } catch (error) {
      console.error('❌ Error loading cities:', error);
      setAvailableCities([]);
    } finally {
      setLoadingCities(false);
    }
  };



  // Handle client deletion
  const handleDeleteClient = async (clientId) => {
    if (!window.confirm('Are you sure you want to delete this client? This action cannot be undone.')) {
      return;
    }

    try {
      setDeleteLoading(true);
      console.log('🗑️ Deleting client:', clientId);

      // Import and use database service
      const { deleteClient } = await import('../services/clientService');
      await deleteClient(clientId);

      // Remove client from local state
      setClients(prevClients => prevClients.filter(client => client.id !== clientId));

      console.log('✅ Client deleted successfully');
      alert('Client deleted successfully!');
    } catch (error) {
      console.error('❌ Error deleting client:', error);
      alert('Failed to delete client. Please try again.');
    } finally {
      setDeleteLoading(false);
    }
  };



  const handleToggleStatus = async (client) => {
    const newStatus = client.isActive ? 'Inactive' : 'Active';
    const action = newStatus === 'Active' ? 'activate' : 'deactivate';

    if (window.confirm(`Are you sure you want to ${action} "${client.company_name}"?`)) {
      try {
        // Import and use database service
        const { updateClient } = await import('../services/clientService');

        // Map the client data to the expected format
        const clientData = {
          companyName: client.company_name,
          companyType: client.company_type,
          onboardingDate: client.onboarding_date,
          emails: client.emails,
          phones: client.phones,
          address: client.address,
          country: client.country,
          state: client.state,
          city: client.city,
          dpiitRegistered: client.dpiit_registered,
          dpiitNumber: client.dpiit_number,
          files: client.files,
          status: newStatus
        };

        await updateClient(client.id, clientData);

        // Reload clients data from database (without loading spinner)
        await loadClients(false);

        console.log(`✅ Client ${action}d successfully`);
        alert(`Client ${action}d successfully!`);
      } catch (error) {
        console.error(`❌ Error ${action}ing client:`, error);
        alert(`Failed to ${action} client. Please try again.`);
      }
    }
  };

  const columns = [
    {
      key: 'company_name',
      label: 'Company Name',
      sortable: true,
      filterable: true
    },
    {
      key: 'company_type',
      label: 'Company Type',
      sortable: true,
      filterable: true
    },
    {
      key: 'email',
      label: 'Email',
      sortable: true,
      filterable: true,
      render: (value, row) => (
        <div>
          {Array.isArray(row.emails) ? row.emails[0] : row.email}
          {Array.isArray(row.emails) && row.emails.length > 1 && (
            <span className="text-xs text-gray-500 block">+{row.emails.length - 1} more</span>
          )}
        </div>
      )
    },
    {
      key: 'phone',
      label: 'Phone',
      sortable: false,
      filterable: true,
      render: (value, row) => (
        <div>
          {Array.isArray(row.phones) ? row.phones[0] : row.phone}
          {Array.isArray(row.phones) && row.phones.length > 1 && (
            <span className="text-xs text-gray-500 block">+{row.phones.length - 1} more</span>
          )}
        </div>
      )
    },
    {
      key: 'onboarding_date',
      label: 'Onboarding Date',
      sortable: true,
      filterable: true
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      filterable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'Active'
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'created_at',
      label: 'Created Date',
      sortable: true,
      filterable: false,
      render: (value) => (
        <div className="text-sm text-gray-500">
          {value ? new Date(value).toLocaleDateString() : 'N/A'}
        </div>
      )
    },
    {
      key: 'updated_at',
      label: 'Updated Date',
      sortable: true,
      filterable: false,
      render: (value) => (
        <div className="text-sm text-gray-500">
          {value ? new Date(value).toLocaleDateString() : 'N/A'}
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      filterable: false,
      render: (value, row) => (
        <div className="flex space-x-2">
          <button
            onClick={() => navigate(`/clients/${row.id}`)}
            className="text-gray-600 hover:text-gray-900 p-1 hover:bg-gray-50 rounded"
            title="View Client"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => navigate(`/clients/${row.id}/edit`)}
            className="text-blue-600 hover:text-blue-900 p-1 hover:bg-blue-50 rounded"
            title="Edit Client"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleToggleStatus(row)}
            className={`p-1 rounded ${
              row.isActive
                ? "text-orange-600 hover:text-orange-900 hover:bg-orange-50"
                : "text-green-600 hover:text-green-900 hover:bg-green-50"
            }`}
            title={row.isActive ? "Deactivate Client" : "Activate Client"}
          >
            {row.isActive ? <XCircleIcon className="h-4 w-4" /> : <CheckCircleIcon className="h-4 w-4" />}
          </button>
          <button
            onClick={() => handleDeleteClient(row.id)}
            disabled={deleteLoading}
            className="text-red-600 hover:text-red-900 p-1 hover:bg-red-50 rounded disabled:opacity-50"
            title="Delete Client"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear validation errors when user starts typing
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }

    // Handle country change
    if (name === 'country') {
      if (value === 'India') {
        // Load states for India from local JSON
        loadStates();
      } else {
        // For other countries, use fallback data
        setAvailableStates(Object.keys(countryStateCity[value] || {}));
      }
      setAvailableCities([]);
      setFormData(prev => ({
        ...prev,
        state: '',
        city: ''
      }));
    }

    // Handle state change
    if (name === 'state') {
      if (formData.country === 'India') {
        // Load cities from local JSON for Indian states
        loadCities(value);
      } else {
        // Use fallback data for other countries
        setAvailableCities(countryStateCity[formData.country]?.[value] || []);
      }
      setFormData(prev => ({
        ...prev,
        city: ''
      }));
    }
  };



  const addEmailField = () => {
    setFormData(prev => ({
      ...prev,
      emails: [...prev.emails, '']
    }));
  };

  const removeEmailField = (index) => {
    setFormData(prev => ({
      ...prev,
      emails: prev.emails.filter((_, i) => i !== index)
    }));
  };

  const handleEmailChange = (index, value) => {
    setFormData(prev => ({
      ...prev,
      emails: prev.emails.map((email, i) => i === index ? value : email)
    }));

    // Clear validation errors for emails when user starts typing
    if (validationErrors.emails) {
      setValidationErrors(prev => ({
        ...prev,
        emails: null
      }));
    }
  };

  const addPhoneField = () => {
    setFormData(prev => ({
      ...prev,
      phones: [...prev.phones, '']
    }));
  };

  const removePhoneField = (index) => {
    setFormData(prev => ({
      ...prev,
      phones: prev.phones.filter((_, i) => i !== index)
    }));
  };

  const handlePhoneChange = (index, value) => {
    setFormData(prev => ({
      ...prev,
      phones: prev.phones.map((phone, i) => i === index ? value : phone)
    }));

    // Clear validation errors for phones when user starts typing
    if (validationErrors.phones) {
      setValidationErrors(prev => ({
        ...prev,
        phones: null
      }));
    }
  };

  const handleFileUpload = (fileType, files) => {
    if (!files || files.length === 0) return;

    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    const validFiles = [];
    const errors = [];

    Array.from(files).forEach(file => {
      // Check file size
      if (file.size > maxSize) {
        errors.push(`${file.name} exceeds 10MB limit`);
        return;
      }

      // Handle duplicate filename by appending timestamp
      const existingFiles = uploadedFiles[fileType] || [];
      const existingNames = existingFiles.map(f => f.name);
      let fileName = file.name;

      if (existingNames.includes(fileName)) {
        const timestamp = new Date().getTime();
        const fileExtension = fileName.substring(fileName.lastIndexOf('.'));
        const fileNameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
        fileName = `${fileNameWithoutExt}_${timestamp}${fileExtension}`;
      }

      // Create new file object with updated name
      const newFile = new File([file], fileName, { type: file.type });
      validFiles.push(newFile);
    });

    if (errors.length > 0) {
      alert(errors.join('\n'));
    }

    if (validFiles.length > 0) {
      setUploadedFiles(prev => ({
        ...prev,
        [fileType]: [...(prev[fileType] || []), ...validFiles]
      }));
    }
  };

  const removeFile = (fileType, index) => {
    setUploadedFiles(prev => ({
      ...prev,
      [fileType]: prev[fileType].filter((_, i) => i !== index)
    }));
  };

  const handleFilesChange = (files) => {
    setUploadedFiles(files);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Comprehensive validation
    setValidationErrors({});
    const errors = {};

    // Validate company name
    const companyValidation = validateCompanyName(formData.companyName);
    if (!companyValidation.isValid) {
      errors.companyName = companyValidation.errors;
    }

    // Validate emails
    const emailValidation = validateEmails(formData.emails);
    if (!emailValidation.isValid) {
      errors.emails = emailValidation.errors;
    }

    // Validate phone numbers
    const phoneValidation = validatePhones(formData.phones, 'IN');
    if (!phoneValidation.isValid) {
      errors.phones = phoneValidation.errors;
    }

    // Check if there are any validation errors
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);

      // Show first error in alert
      const firstError = Object.values(errors)[0];
      const errorMessage = Array.isArray(firstError) ? firstError[0] : firstError;
      alert(`❌ Validation Error: ${errorMessage}`);
      return;
    }

    try {
      setLoading(true);
      console.log('🚀 Submitting client form...');
      console.log('📋 Form data:', formData);
      console.log('📁 Uploaded files:', uploadedFiles);

      // Create client object
      const clientData = {
        companyName: formData.companyName,
        companyType: formData.companyType,
        emails: formData.emails.filter(email => email.trim() !== ''),
        phones: formData.phones.filter(phone => phone.trim() !== ''),
        address: formData.address,
        country: formData.country,
        state: formData.state,
        city: formData.city,
        dpiitRegistered: formData.dpiitRegistered === 'yes',
        dpiitNumber: formData.dpiitRegistered === 'yes' ? formData.validTill : null,
        files: uploadedFiles,
        status: 'Active'
      };

      console.log('💾 Sending client data to service:', clientData);

      // Save to database
      const { createClient } = await import('../services/clientService');
      const result = await createClient(clientData);
      console.log('✅ Client created successfully:', result);

      // Reload clients list
      const { getAllClients } = await import('../services/clientService');
      const updatedClients = await getAllClients();
      setClients(updatedClients);

      // Reset form
      setFormData({
        companyType: '',
        companyName: '',
        emails: [''],
        phones: [''],
        address: '',
        country: '',
        state: '',
        city: '',
        username: '',
        gstNumber: '',
        dpiitRegistered: '',
        validTill: '',
        website: '',
        description: '',
        creditLimit: '',
        paymentTerms: ''
      });
      setUploadedFiles({
        dpiitCertificate: [],
        tdsFile: [],
        gstFile: [],
        ndaFile: [],
        agreementFile: [],
        quotationFile: [],
        panCardFile: [],
        udhyamRegistrationFile: [],
        othersFile: []
      });
      setAvailableStates([]);
      setAvailableCities([]);
      setShowAddForm(false);

      // Success notification
      alert('✅ Client added successfully! All files have been saved locally.');
    } catch (err) {
      console.error('❌ Error creating client:', err);
      alert(`❌ Failed to create client: ${err.message || 'Please try again.'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Clients</h1>
          <p className="mt-2 text-gray-600">Manage your client relationships</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-primary flex items-center"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add New Client
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Total Clients */}
        <div
          className={`bg-white rounded-lg shadow-sm border-2 p-6 cursor-pointer transition-all duration-200 ${
            filterStatus === 'all'
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-200 hover:border-blue-300'
          }`}
          onClick={() => setFilterStatus('all')}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Total Clients</dt>
                <dd className="text-3xl font-bold text-gray-900">{totalClients}</dd>
              </dl>
            </div>
            <div className="flex-shrink-0">
              {filterStatus === 'all' && (
                <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </div>
          </div>
          <div className="mt-2">
            <p className="text-sm text-gray-600">
              {filterStatus === 'all' ? '✓ Showing all clients' : 'Click to show all clients'}
            </p>
          </div>
        </div>

        {/* Active Clients */}
        <div
          className={`bg-white rounded-lg shadow-sm border-2 p-6 cursor-pointer transition-all duration-200 ${
            filterStatus === 'active'
              ? 'border-green-500 bg-green-50'
              : 'border-gray-200 hover:border-green-300'
          }`}
          onClick={() => setFilterStatus('active')}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Active Clients</dt>
                <dd className="text-3xl font-bold text-green-600">{activeClients}</dd>
              </dl>
            </div>
            <div className="flex-shrink-0">
              {filterStatus === 'active' && (
                <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </div>
          </div>
          <div className="mt-2">
            <p className="text-sm text-gray-600">
              {filterStatus === 'active' ? '✓ Showing active clients' : 'Click to show active clients'}
            </p>
          </div>
        </div>

        {/* Inactive Clients */}
        <div
          className={`bg-white rounded-lg shadow-sm border-2 p-6 cursor-pointer transition-all duration-200 ${
            filterStatus === 'inactive'
              ? 'border-red-500 bg-red-50'
              : 'border-gray-200 hover:border-red-300'
          }`}
          onClick={() => setFilterStatus('inactive')}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Inactive Clients</dt>
                <dd className="text-3xl font-bold text-red-600">{inactiveClients}</dd>
              </dl>
            </div>
            <div className="flex-shrink-0">
              {filterStatus === 'inactive' && (
                <svg className="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </div>
          </div>
          <div className="mt-2">
            <p className="text-sm text-gray-600">
              {filterStatus === 'inactive' ? '✓ Showing inactive clients' : 'Click to show inactive clients'}
            </p>
          </div>
        </div>
      </div>

      {/* Clients Table */}
      <DataTable
        data={filteredClients}
        columns={columns}
        title="Clients Management"
        defaultPageSize={50}
        enableExport={true}
        enableColumnToggle={true}
        enableFiltering={true}
        enableSorting={true}
        actionIcons={
          <>
            <div className="flex items-center space-x-2">
              <EyeIcon className="h-4 w-4 text-gray-600" />
              <span className="text-sm text-gray-600">View</span>
            </div>
            <div className="flex items-center space-x-2">
              <PencilIcon className="h-4 w-4 text-blue-600" />
              <span className="text-sm text-blue-600">Edit</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircleIcon className="h-4 w-4 text-green-600" />
              <span className="text-sm text-green-600">Toggle Active</span>
            </div>
            <div className="flex items-center space-x-2">
              <XCircleIcon className="h-4 w-4 text-orange-600" />
              <span className="text-sm text-orange-600">Toggle Inactive</span>
            </div>
            <div className="flex items-center space-x-2">
              <TrashIcon className="h-4 w-4 text-red-600" />
              <span className="text-sm text-red-600">Delete</span>
            </div>
          </>
        }
      />

      {/* Add Client Modal */}
      {showAddForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => setShowAddForm(false)}></div>
            <div className="relative bg-white rounded-lg max-w-7xl w-full max-h-[90vh] overflow-y-auto">
              <form onSubmit={handleSubmit} className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Add New Client</h3>
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    ×
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Basic Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900">Basic Information</h4>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Type of Company *
                      </label>
                      <select
                        name="companyType"
                        value={formData.companyType}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                      >
                        <option value="">Select company type</option>
                        {companyTypeOptions.map((type) => (
                          <option key={type.id} value={type.name}>
                            {type.name}
                          </option>
                        ))}
                      </select>
                      {companyTypeOptions.length === 0 && (
                        <p className="mt-1 text-sm text-gray-500">
                          Loading company types...
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Company Name / Individual Name *
                      </label>
                      <input
                        type="text"
                        name="companyName"
                        value={formData.companyName}
                        onChange={handleInputChange}
                        required
                        className={`input-field ${
                          validationErrors.companyName ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                        }`}
                        placeholder="Enter company or individual name"
                      />
                      {/* Company name validation errors */}
                      {validationErrors.companyName && (
                        <div className="mt-1 p-2 bg-red-50 border border-red-200 rounded">
                          {validationErrors.companyName.map((error, index) => (
                            <p key={index} className="text-red-600 text-xs">
                              {error}
                            </p>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Email Addresses */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email Addresses *
                      </label>
                      {formData.emails.map((email, index) => (
                        <div key={index} className="flex items-center space-x-2 mb-2">
                          <input
                            type="email"
                            value={email}
                            onChange={(e) => {
                              const newEmails = [...formData.emails];
                              newEmails[index] = e.target.value;
                              setFormData({ ...formData, emails: newEmails });
                            }}
                            className={`flex-1 input-field ${
                              validationErrors.emails ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                            }`}
                            placeholder="Enter email address"
                            required={index === 0}
                          />
                          {formData.emails.length > 1 && (
                            <button
                              type="button"
                              onClick={() => {
                                const newEmails = formData.emails.filter((_, i) => i !== index);
                                setFormData({ ...formData, emails: newEmails });
                              }}
                              className="px-2 py-1 text-red-600 hover:text-red-800"
                            >
                              ✕
                            </button>
                          )}
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={() => setFormData({ ...formData, emails: [...formData.emails, ''] })}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        + Add Another Email
                      </button>
                      {/* Email validation errors */}
                      {validationErrors.emails && (
                        <div className="mt-1 p-2 bg-red-50 border border-red-200 rounded">
                          {Array.isArray(validationErrors.emails) ? (
                            validationErrors.emails.map((error, index) => (
                              <p key={index} className="text-red-600 text-xs">
                                {error}
                              </p>
                            ))
                          ) : (
                            <p className="text-red-600 text-xs">{validationErrors.emails}</p>
                          )}
                        </div>
                      )}
                    </div>
                    {/* Phone Numbers */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Phone Numbers *
                      </label>
                      {formData.phones.map((phone, index) => (
                        <div key={index} className="flex items-center space-x-2 mb-2">
                          <input
                            type="tel"
                            value={phone}
                            onChange={(e) => {
                              const newPhones = [...formData.phones];
                              newPhones[index] = e.target.value;
                              setFormData({ ...formData, phones: newPhones });
                            }}
                            className={`flex-1 input-field ${
                              validationErrors.phones ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                            }`}
                            placeholder="Enter phone number"
                            required={index === 0}
                          />
                          {formData.phones.length > 1 && (
                            <button
                              type="button"
                              onClick={() => {
                                const newPhones = formData.phones.filter((_, i) => i !== index);
                                setFormData({ ...formData, phones: newPhones });
                              }}
                              className="px-2 py-1 text-red-600 hover:text-red-800"
                            >
                              ✕
                            </button>
                          )}
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={() => setFormData({ ...formData, phones: [...formData.phones, ''] })}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        + Add Another Phone
                      </button>
                      {/* Phone validation errors */}
                      {validationErrors.phones && (
                        <div className="mt-1 p-2 bg-red-50 border border-red-200 rounded">
                          {Array.isArray(validationErrors.phones) ? (
                            validationErrors.phones.map((error, index) => (
                              <p key={index} className="text-red-600 text-xs">
                                {error}
                              </p>
                            ))
                          ) : (
                            <p className="text-red-600 text-xs">{validationErrors.phones}</p>
                          )}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Company's Address *
                      </label>
                      <textarea
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        required
                        rows={3}
                        className="input-field"
                        placeholder="Complete company address"
                      />
                    </div>
                  </div>

                  {/* Location and Additional Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900">Location & Additional Information</h4>

                    <LocationSelector
                      formData={formData}
                      setFormData={setFormData}
                      validationErrors={validationErrors}
                    />

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Username
                      </label>
                      <input
                        type="text"
                        name="username"
                        value={formData.username}
                        onChange={handleInputChange}
                        className="input-field"
                        placeholder="Enter username"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        GST Number
                      </label>
                      <input
                        type="text"
                        name="gstNumber"
                        value={formData.gstNumber}
                        onChange={handleInputChange}
                        className="input-field"
                        placeholder="Enter GST number"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        DPIIT Registration *
                      </label>
                      <select
                        name="dpiitRegistered"
                        value={formData.dpiitRegistered}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                      >
                        <option value="">Select option</option>
                        <option value="yes">Yes</option>
                        <option value="no">No</option>
                      </select>
                    </div>

                    {formData.dpiitRegistered === 'yes' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          DPIIT Number *
                        </label>
                        <input
                          type="text"
                          name="validTill"
                          value={formData.validTill}
                          onChange={handleInputChange}
                          required
                          className="input-field"
                          placeholder="Enter DPIIT number"
                        />
                      </div>
                    )}

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Website
                      </label>
                      <input
                        type="url"
                        name="website"
                        value={formData.website}
                        onChange={handleInputChange}
                        className="input-field"
                        placeholder="https://example.com"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Credit Limit
                      </label>
                      <input
                        type="number"
                        name="creditLimit"
                        value={formData.creditLimit}
                        onChange={handleInputChange}
                        className="input-field"
                        placeholder="Enter credit limit"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Payment Terms
                      </label>
                      <input
                        type="text"
                        name="paymentTerms"
                        value={formData.paymentTerms}
                        onChange={handleInputChange}
                        className="input-field"
                        placeholder="Enter payment terms"
                      />
                    </div>
                  </div>
                </div>

                {/* Description */}
                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={4}
                    className="input-field"
                    placeholder="Enter company description"
                  />
                </div>

                {/* File Upload Section */}
                <div className="mt-6">
                  <h4 className="text-md font-medium text-gray-900 mb-4">File Upload Section</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                    {/* DPIIT Certificate */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        DPIIT Certificate
                      </label>
                      <S3FileUpload
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            dpiitCertificate: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            dpiitCertificate: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.dpiitCertificate || []}
                        maxFiles={5}
                        maxSizePerFile={10}
                        acceptedFileTypes={['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']}
                      />
                    </div>

                    {/* TDS File */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        TDS File
                      </label>
                      <S3FileUpload
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            tdsFile: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            tdsFile: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.tdsFile || []}
                        maxFiles={5}
                        maxSizePerFile={10}
                        acceptedFileTypes={['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']}
                      />
                    </div>

                    {/* GST File */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        GST File
                      </label>
                      <S3FileUpload
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            gstFile: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            gstFile: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.gstFile || []}
                        maxFiles={5}
                        maxSizePerFile={10}
                        acceptedFileTypes={['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']}
                      />
                    </div>

                    {/* NDA File */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        NDA File
                      </label>
                      <S3FileUpload
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            ndaFile: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            ndaFile: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.ndaFile || []}
                        maxFiles={5}
                        maxSizePerFile={10}
                        acceptedFileTypes={['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']}
                      />
                    </div>

                    {/* Agreement File */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Agreement File
                      </label>
                      <S3FileUpload
                        onFilesUploaded={(files) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            agreementFile: files
                          }));
                        }}
                        onFileDeleted={(deletedFile, remainingFiles) => {
                          setUploadedFiles(prev => ({
                            ...prev,
                            agreementFile: remainingFiles
                          }));
                        }}
                        existingFiles={uploadedFiles.agreementFile || []}
                        maxFiles={5}
                        maxSizePerFile={10}
                        acceptedFileTypes={['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']}
                      />
                    </div>
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                  >
                    Save Client
                  </button>
                </div>


              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Clients;
