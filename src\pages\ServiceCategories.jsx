import { useState, useEffect } from 'react';
import { PlusIcon, PencilIcon, TrashIcon, XMarkIcon, EyeIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import DataTable from '../components/DataTable/DataTable';
import { getAllServiceCategories, createServiceCategory, updateServiceCategory, deleteServiceCategory, hardDeleteServiceCategory } from '../services/serviceCategoriesService';

const ServiceCategories = () => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#6B7280',
    isActive: true
  });
  const [validationErrors, setValidationErrors] = useState({});
  const [statusFilter, setStatusFilter] = useState('all'); // 'all', 'active', 'inactive'
  const [showViewModal, setShowViewModal] = useState(false);
  const [viewingCategory, setViewingCategory] = useState(null);

  // Filter categories based on status
  const getFilteredCategories = () => {
    switch (statusFilter) {
      case 'active':
        return categories.filter(category => category.isActive);
      case 'inactive':
        return categories.filter(category => !category.isActive);
      default:
        return categories;
    }
  };

  // Load categories on component mount
  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const categoriesData = await getAllServiceCategories(true); // Include inactive categories
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading service categories:', error);
      alert('Failed to load service categories');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear validation error for this field
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  const validateForm = () => {
    const errors = {};
    
    if (!formData.name.trim()) {
      errors.name = 'Category name is required';
    }
    
    if (formData.name.trim().length < 2) {
      errors.name = 'Category name must be at least 2 characters';
    }

    // Validate color format
    if (formData.color && !/^#[0-9A-F]{6}$/i.test(formData.color)) {
      errors.color = 'Color must be a valid hex color (e.g., #6B7280)';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      
      if (editingCategory) {
        await updateServiceCategory(editingCategory.id, formData);
        alert('Service category updated successfully!');
      } else {
        await createServiceCategory(formData);
        alert('Service category created successfully!');
      }
      
      await loadCategories();
      resetForm();
    } catch (error) {
      console.error('Error saving service category:', error);
      alert(error.message || 'Failed to save service category');
    } finally {
      setLoading(false);
    }
  };

  const handleView = (category) => {
    setViewingCategory(category);
    setShowViewModal(true);
  };

  const handleEdit = (category) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      description: category.description || '',
      color: category.color || '#6B7280',
      isActive: category.isActive
    });
    setShowEditForm(true);
  };

  const handleToggleStatus = async (category) => {
    const newStatus = !category.isActive;
    const action = newStatus ? 'activate' : 'deactivate';

    if (window.confirm(`Are you sure you want to ${action} "${category.name}"?`)) {
      try {
        await updateServiceCategory(category.id, { ...category, isActive: newStatus });
        await loadCategories();
        alert(`✅ Category ${action}d successfully!`);
      } catch (error) {
        console.error(`Error ${action}ing category:`, error);
        alert(`❌ Failed to ${action} category. Please try again.`);
      }
    }
  };

  const handleDelete = async (category) => {
    const confirmMessage = category.isActive 
      ? `Are you sure you want to deactivate "${category.name}"?\n\nThis will hide it from service forms but keep it in the database for existing records.`
      : `Are you sure you want to permanently delete "${category.name}"?\n\nThis action cannot be undone.`;
    
    if (window.confirm(confirmMessage)) {
      try {
        setLoading(true);
        console.log('🔄 Attempting to delete service category:', category.id, category.name);
        
        let result;
        if (category.isActive) {
          // Soft delete for active categories
          result = await deleteServiceCategory(category.id);
          alert('Service category deactivated successfully!');
        } else {
          // Hard delete for inactive categories
          result = await hardDeleteServiceCategory(category.id);
          alert('Service category permanently deleted!');
        }
        
        await loadCategories();
      } catch (error) {
        console.error('❌ Error deleting service category:', error);
        alert(error.message || 'Failed to delete service category');
      } finally {
        setLoading(false);
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      color: '#6B7280',
      isActive: true
    });
    setValidationErrors({});
    setShowAddForm(false);
    setShowEditForm(false);
    setEditingCategory(null);
  };

  const columns = [
    {
      key: 'name',
      label: 'Category Name',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center space-x-3">
          <div 
            className="w-4 h-4 rounded-full border border-gray-300"
            style={{ backgroundColor: row.color }}
          ></div>
          <div className="font-medium text-gray-900">{value}</div>
        </div>
      )
    },
    {
      key: 'description',
      label: 'Description',
      sortable: false,
      render: (value) => (
        <div className="max-w-md text-gray-600 truncate" title={value}>
          {value}
        </div>
      )
    },
    {
      key: 'color',
      label: 'Color',
      render: (value) => (
        <div className="flex items-center space-x-2">
          <div 
            className="w-6 h-6 rounded border border-gray-300"
            style={{ backgroundColor: value }}
          ></div>
          <span className="text-sm text-gray-600 font-mono">{value}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value, row) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          row.isActive 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {row.isActive ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      key: 'createdAt',
      label: 'Created',
      sortable: true,
      render: (value) => (
        <div className="text-sm text-gray-500">
          {new Date(value).toLocaleDateString()}
        </div>
      )
    },
    {
      key: 'updatedAt',
      label: 'Updated',
      sortable: true,
      render: (value) => (
        <div className="text-sm text-gray-500">
          {value ? new Date(value).toLocaleDateString() : 'N/A'}
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      filterable: false,
      render: (_, row) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleView(row)}
            className="text-gray-600 hover:text-gray-900"
            title="View Category"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleEdit(row)}
            className="text-blue-600 hover:text-blue-900"
            title="Edit Category"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleToggleStatus(row)}
            className={row.isActive ? "text-orange-600 hover:text-orange-900" : "text-green-600 hover:text-green-900"}
            title={row.isActive ? "Deactivate Category" : "Activate Category"}
          >
            {row.isActive ? <XCircleIcon className="h-4 w-4" /> : <CheckCircleIcon className="h-4 w-4" />}
          </button>
          <button
            onClick={() => handleDelete(row)}
            className="text-red-600 hover:text-red-900"
            title={row.isActive ? "Deactivate Category" : "Permanently Delete Category"}
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ];

  const predefinedColors = [
    '#6B7280', '#EF4444', '#F59E0B', '#10B981', '#3B82F6', 
    '#8B5CF6', '#EC4899', '#F97316', '#84CC16', '#06B6D4'
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-gray-900">Service Categories</h1>
          <p className="mt-1 sm:mt-2 text-sm text-gray-700">
            Manage categories for organizing services
          </p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="btn-primary flex items-center justify-center space-x-2 w-full sm:w-auto"
        >
          <PlusIcon className="h-4 w-4" />
          <span className="text-sm sm:text-base">Add Category</span>
        </button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Total Categories */}
        <div
          onClick={() => setStatusFilter('all')}
          className={`bg-white rounded-lg shadow-sm border p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            statusFilter === 'all'
              ? 'border-blue-500 ring-2 ring-blue-200 bg-blue-50'
              : 'border-gray-200 hover:border-blue-300'
          }`}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                statusFilter === 'all' ? 'bg-blue-200' : 'bg-blue-100'
              }`}>
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Categories</p>
              <p className={`text-2xl font-bold ${
                statusFilter === 'all' ? 'text-blue-700' : 'text-gray-900'
              }`}>{categories.length}</p>
            </div>
          </div>
          {statusFilter === 'all' && (
            <div className="mt-2 text-xs text-blue-600 font-medium">
              ✓ Showing all categories
            </div>
          )}
        </div>

        {/* Active Categories */}
        <div
          onClick={() => setStatusFilter('active')}
          className={`bg-white rounded-lg shadow-sm border p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            statusFilter === 'active'
              ? 'border-green-500 ring-2 ring-green-200 bg-green-50'
              : 'border-gray-200 hover:border-green-300'
          }`}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                statusFilter === 'active' ? 'bg-green-200' : 'bg-green-100'
              }`}>
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Categories</p>
              <p className={`text-2xl font-bold ${
                statusFilter === 'active' ? 'text-green-700' : 'text-green-600'
              }`}>
                {categories.filter(category => category.isActive).length}
              </p>
            </div>
          </div>
          {statusFilter === 'active' && (
            <div className="mt-2 text-xs text-green-600 font-medium">
              ✓ Showing active categories only
            </div>
          )}
        </div>

        {/* Inactive Categories */}
        <div
          onClick={() => setStatusFilter('inactive')}
          className={`bg-white rounded-lg shadow-sm border p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            statusFilter === 'inactive'
              ? 'border-red-500 ring-2 ring-red-200 bg-red-50'
              : 'border-gray-200 hover:border-red-300'
          }`}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                statusFilter === 'inactive' ? 'bg-red-200' : 'bg-red-100'
              }`}>
                <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Inactive Categories</p>
              <p className={`text-2xl font-bold ${
                statusFilter === 'inactive' ? 'text-red-700' : 'text-red-600'
              }`}>
                {categories.filter(category => !category.isActive).length}
              </p>
            </div>
          </div>
          {statusFilter === 'inactive' && (
            <div className="mt-2 text-xs text-red-600 font-medium">
              ✓ Showing inactive categories only
            </div>
          )}
        </div>
      </div>

      {/* Filter Status Indicator */}
      {statusFilter !== 'all' && (
        <div className="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
            </svg>
            <span className="text-sm font-medium text-blue-800">
              Showing {statusFilter} categories only ({getFilteredCategories().length} of {categories.length} total)
            </span>
          </div>
          <button
            onClick={() => setStatusFilter('all')}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            Clear Filter
          </button>
        </div>
      )}



      {/* Categories Table */}
      <div className="card">
        <DataTable
          data={getFilteredCategories()}
          columns={columns}
          title="Service Categories"
          defaultPageSize={10}
          enableExport={true}
          enableColumnToggle={true}
          enableFiltering={true}
          enableSorting={true}
          loading={loading}
          searchable={true}
          searchPlaceholder="Search categories..."
          emptyMessage="No service categories found"
          actionIcons={
            <>
              <div className="flex flex-col items-center space-y-1">
                <EyeIcon className="h-4 w-4 text-gray-600" />
                <span className="text-xs text-gray-600">View</span>
              </div>

              <div className="flex flex-col items-center space-y-1">
                <PencilIcon className="h-4 w-4 text-blue-600" />
                <span className="text-xs text-blue-600">Edit</span>
              </div>

              <div className="flex flex-col items-center space-y-1">
                <CheckCircleIcon className="h-4 w-4 text-green-600" />
                <span className="text-xs text-green-600">Toggle Active</span>
              </div>

              <div className="flex flex-col items-center space-y-1">
                <XCircleIcon className="h-4 w-4 text-orange-600" />
                <span className="text-xs text-orange-600">Toggle Inactive</span>
              </div>

              <div className="flex flex-col items-center space-y-1">
                <TrashIcon className="h-4 w-4 text-red-600" />
                <span className="text-xs text-red-600">Delete</span>
              </div>
            </>
          }
        />
      </div>

      {/* Add Category Modal */}
      {showAddForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={resetForm}></div>
            <div className="relative bg-white rounded-lg max-w-md w-full p-6">
              <form onSubmit={handleSubmit}>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Add New Category</h3>
                  <button
                    type="button"
                    onClick={resetForm}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category Name *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className={`input-field ${validationErrors.name ? 'border-red-500' : ''}`}
                      placeholder="Enter category name"
                      required
                    />
                    {validationErrors.name && (
                      <p className="text-red-500 text-xs mt-1">{validationErrors.name}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={3}
                      className="input-field"
                      placeholder="Enter category description"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Color
                    </label>
                    <div className="flex items-center space-x-2 mb-2">
                      <input
                        type="color"
                        name="color"
                        value={formData.color}
                        onChange={handleInputChange}
                        className="w-10 h-10 rounded border border-gray-300"
                      />
                      <input
                        type="text"
                        name="color"
                        value={formData.color}
                        onChange={handleInputChange}
                        className={`input-field flex-1 font-mono ${validationErrors.color ? 'border-red-500' : ''}`}
                        placeholder="#6B7280"
                      />
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {predefinedColors.map(color => (
                        <button
                          key={color}
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, color }))}
                          className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
                          style={{ backgroundColor: color }}
                          title={color}
                        />
                      ))}
                    </div>
                    {validationErrors.color && (
                      <p className="text-red-500 text-xs mt-1">{validationErrors.color}</p>
                    )}
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleInputChange}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label className="ml-2 text-sm text-gray-700">
                      Active (available for selection)
                    </label>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                    disabled={loading}
                  >
                    {loading ? 'Creating...' : 'Create Category'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit Category Modal */}
      {showEditForm && editingCategory && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={resetForm}></div>
            <div className="relative bg-white rounded-lg max-w-md w-full p-6">
              <form onSubmit={handleSubmit}>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Edit Category</h3>
                  <button
                    type="button"
                    onClick={resetForm}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category Name *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className={`input-field ${validationErrors.name ? 'border-red-500' : ''}`}
                      placeholder="Enter category name"
                      required
                    />
                    {validationErrors.name && (
                      <p className="text-red-500 text-xs mt-1">{validationErrors.name}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={3}
                      className="input-field"
                      placeholder="Enter category description"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Color
                    </label>
                    <div className="flex items-center space-x-2 mb-2">
                      <input
                        type="color"
                        name="color"
                        value={formData.color}
                        onChange={handleInputChange}
                        className="w-10 h-10 rounded border border-gray-300"
                      />
                      <input
                        type="text"
                        name="color"
                        value={formData.color}
                        onChange={handleInputChange}
                        className={`input-field flex-1 font-mono ${validationErrors.color ? 'border-red-500' : ''}`}
                        placeholder="#6B7280"
                      />
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {predefinedColors.map(color => (
                        <button
                          key={color}
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, color }))}
                          className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
                          style={{ backgroundColor: color }}
                          title={color}
                        />
                      ))}
                    </div>
                    {validationErrors.color && (
                      <p className="text-red-500 text-xs mt-1">{validationErrors.color}</p>
                    )}
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleInputChange}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label className="ml-2 text-sm text-gray-700">
                      Active (available for selection)
                    </label>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                    disabled={loading}
                  >
                    {loading ? 'Updating...' : 'Update Category'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* View Category Modal */}
      {showViewModal && viewingCategory && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => setShowViewModal(false)}></div>
            <div className="relative bg-white rounded-lg max-w-2xl w-full p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">Category Details</h3>
                <button
                  onClick={() => setShowViewModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-6">
                {/* Category Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Category Name</label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-4 h-4 rounded-full border border-gray-300"
                        style={{ backgroundColor: viewingCategory.color }}
                      ></div>
                      <p className="text-gray-900 font-medium">{viewingCategory.name}</p>
                    </div>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    <p className="text-gray-900">{viewingCategory.description || 'No description provided'}</p>
                  </div>
                </div>

                {/* Color */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Color</label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-8 h-8 rounded border border-gray-300"
                        style={{ backgroundColor: viewingCategory.color }}
                      ></div>
                      <span className="text-gray-900 font-mono">{viewingCategory.color}</span>
                    </div>
                  </div>
                </div>

                {/* Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      viewingCategory.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {viewingCategory.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>

                {/* Timestamps */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Created</label>
                    <div className="p-3 bg-gray-50 rounded-md">
                      <p className="text-gray-900">{new Date(viewingCategory.createdAt).toLocaleDateString()}</p>
                      <p className="text-xs text-gray-500">{new Date(viewingCategory.createdAt).toLocaleTimeString()}</p>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Updated</label>
                    <div className="p-3 bg-gray-50 rounded-md">
                      <p className="text-gray-900">
                        {viewingCategory.updatedAt ? new Date(viewingCategory.updatedAt).toLocaleDateString() : 'N/A'}
                      </p>
                      {viewingCategory.updatedAt && (
                        <p className="text-xs text-gray-500">{new Date(viewingCategory.updatedAt).toLocaleTimeString()}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                <button
                  onClick={() => setShowViewModal(false)}
                  className="btn-secondary"
                >
                  Close
                </button>
                <button
                  onClick={() => {
                    setShowViewModal(false);
                    handleEdit(viewingCategory);
                  }}
                  className="btn-primary"
                >
                  Edit Category
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ServiceCategories;
