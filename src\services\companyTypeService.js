import { sql } from '../config/database.js';

/**
 * Company Type Service
 * Handles CRUD operations for company types
 */

// Check table structure first
export const checkCompanyTypesTable = async () => {
  try {
    console.log('🔍 Checking company_types table structure...');

    // Check if table exists
    const tableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'company_types'
      )
    `;

    if (!tableExists[0].exists) {
      console.log('❌ company_types table does not exist');
      return { exists: false };
    }

    // Get table structure
    const columns = await sql`
      SELECT column_name, data_type, column_default, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'company_types'
      ORDER BY ordinal_position
    `;

    console.log('📋 Table structure:', columns);
    return { exists: true, columns };
  } catch (error) {
    console.error('❌ Error checking table structure:', error);
    throw error;
  }
};

// Get all company types
export const getAllCompanyTypes = async (includeInactive = false) => {
  try {
    console.log('🔍 Fetching company types from database...');

    // First check what columns exist
    const tableInfo = await checkCompanyTypesTable();
    if (!tableInfo.exists) {
      console.log('❌ Table does not exist');
      return [];
    }

    const columnNames = tableInfo.columns.map(col => col.column_name);
    console.log('📋 Available columns:', columnNames);

    // Check what columns are available
    const hasCreatedAt = columnNames.includes('created_at');
    const hasUpdatedAt = columnNames.includes('updated_at');
    const hasIsActive = columnNames.includes('isActive');
    const hasStatus = columnNames.includes('status');

    // Use simple query based on available columns
    let companyTypes = [];

    if (hasIsActive && hasCreatedAt && hasUpdatedAt) {
      // Full query with all columns
      if (includeInactive) {
        companyTypes = await sql`
          SELECT
            id,
            name,
            description,
            "isActive",
            created_at as "createdAt",
            updated_at as "updatedAt"
          FROM company_types
          ORDER BY created_at DESC
        `;
      } else {
        companyTypes = await sql`
          SELECT
            id,
            name,
            description,
            "isActive",
            created_at as "createdAt",
            updated_at as "updatedAt"
          FROM company_types
          WHERE "isActive" = true
          ORDER BY created_at DESC
        `;
      }
    } else if (hasIsActive && !hasCreatedAt) {
      // Query without timestamp columns
      if (includeInactive) {
        companyTypes = await sql`
          SELECT
            id,
            name,
            description,
            "isActive",
            CURRENT_TIMESTAMP as "createdAt",
            CURRENT_TIMESTAMP as "updatedAt"
          FROM company_types
          ORDER BY id DESC
        `;
      } else {
        companyTypes = await sql`
          SELECT
            id,
            name,
            description,
            "isActive",
            CURRENT_TIMESTAMP as "createdAt",
            CURRENT_TIMESTAMP as "updatedAt"
          FROM company_types
          WHERE "isActive" = true
          ORDER BY id DESC
        `;
      }
    } else {
      // Minimal query - just basic columns
      companyTypes = await sql`
        SELECT
          id,
          name,
          description,
          true as "isActive",
          CURRENT_TIMESTAMP as "createdAt",
          CURRENT_TIMESTAMP as "updatedAt"
        FROM company_types
        ORDER BY id DESC
      `;
    }

    console.log('✅ Company types fetched successfully:', companyTypes.length);
    console.log('✅ Sample data:', companyTypes.slice(0, 2));
    return companyTypes;

  } catch (error) {
    console.error('❌ Error fetching company types:', error);
    throw error;
  }
};

// Get active company types only (for dropdowns)
export const getActiveCompanyTypes = async () => {
  try {
    console.log('🔍 Fetching active company types...');

    const companyTypes = await sql`
      SELECT 
        id,
        name,
        description
      FROM company_types 
      WHERE "isActive" = true
      ORDER BY name ASC
    `;

    console.log('✅ Active company types fetched:', companyTypes.length);
    return companyTypes;

  } catch (error) {
    console.error('❌ Error fetching active company types:', error);
    throw error;
  }
};

// Create new company type
export const createCompanyType = async (companyTypeData) => {
  try {
    console.log('📝 Creating new company type:', companyTypeData);

    // Check if company type with same name already exists
    const existingType = await sql`
      SELECT id FROM company_types 
      WHERE LOWER(name) = LOWER(${companyTypeData.name})
    `;

    if (existingType.length > 0) {
      throw new Error('Company type with this name already exists');
    }

    // Simple insert - try with all columns first, fallback if needed
    let newCompanyType;

    try {
      // Try with all columns
      newCompanyType = await sql`
        INSERT INTO company_types (
          name,
          description,
          "isActive",
          created_at,
          updated_at
        ) VALUES (
          ${companyTypeData.name},
          ${companyTypeData.description || null},
          ${companyTypeData.isActive !== undefined ? companyTypeData.isActive : true},
          CURRENT_TIMESTAMP,
          CURRENT_TIMESTAMP
        )
        RETURNING *
      `;
    } catch (error) {
      console.log('⚠️ Full insert failed, trying minimal insert:', error.message);

      // Fallback to minimal insert
      try {
        newCompanyType = await sql`
          INSERT INTO company_types (name, description)
          VALUES (${companyTypeData.name}, ${companyTypeData.description || null})
          RETURNING *
        `;
      } catch (fallbackError) {
        console.error('❌ Even minimal insert failed:', fallbackError);
        throw fallbackError;
      }
    }

    console.log('✅ Company type created successfully:', newCompanyType[0]);
    return newCompanyType[0];

  } catch (error) {
    console.error('❌ Error creating company type:', error);
    throw error;
  }
};

// Update company type
export const updateCompanyType = async (id, companyTypeData) => {
  try {
    console.log('📝 Updating company type:', id, companyTypeData);

    // Check if company type exists
    const existingType = await sql`
      SELECT id FROM company_types WHERE id = ${id}
    `;

    if (existingType.length === 0) {
      throw new Error('Company type not found');
    }

    // Check if name conflicts with another company type
    if (companyTypeData.name) {
      const nameConflict = await sql`
        SELECT id FROM company_types 
        WHERE LOWER(name) = LOWER(${companyTypeData.name}) AND id != ${id}
      `;

      if (nameConflict.length > 0) {
        throw new Error('Company type with this name already exists');
      }
    }

    // Update company type
    const updatedCompanyType = await sql`
      UPDATE company_types 
      SET 
        name = COALESCE(${companyTypeData.name}, name),
        description = COALESCE(${companyTypeData.description}, description),
        "isActive" = COALESCE(${companyTypeData.isActive}, "isActive"),
        "updatedAt" = NOW()
      WHERE id = ${id}
      RETURNING *
    `;

    console.log('✅ Company type updated successfully:', updatedCompanyType[0]);
    return updatedCompanyType[0];

  } catch (error) {
    console.error('❌ Error updating company type:', error);
    throw error;
  }
};

// Delete company type (soft delete)
export const deleteCompanyType = async (id) => {
  try {
    console.log('🗑️ Deleting company type:', id);

    // Check if company type exists
    const existingType = await sql`
      SELECT id FROM company_types WHERE id = ${id}
    `;

    if (existingType.length === 0) {
      throw new Error('Company type not found');
    }

    // Check if company type is being used by any vendors or clients
    const usageCheck = await sql`
      SELECT
        (SELECT COUNT(*) FROM vendors WHERE company_type = ${id}) as vendor_count,
        (SELECT COUNT(*) FROM customers WHERE "companyType" = ${id}) as customer_count
    `;

    const { vendor_count, customer_count } = usageCheck[0];
    
    if (vendor_count > 0 || customer_count > 0) {
      // Soft delete - deactivate instead of removing
      const deactivatedType = await sql`
        UPDATE company_types 
        SET 
          "isActive" = false,
          "updatedAt" = NOW()
        WHERE id = ${id}
        RETURNING *
      `;

      console.log('✅ Company type deactivated (in use):', deactivatedType[0]);
      return { 
        success: true, 
        message: `Company type deactivated (used by ${vendor_count} vendors and ${customer_count} customers)`,
        companyType: deactivatedType[0]
      };
    } else {
      // Hard delete if not in use
      await sql`DELETE FROM company_types WHERE id = ${id}`;
      
      console.log('✅ Company type deleted successfully');
      return { 
        success: true, 
        message: 'Company type deleted successfully'
      };
    }

  } catch (error) {
    console.error('❌ Error deleting company type:', error);
    throw error;
  }
};

// Get company type by ID
export const getCompanyTypeById = async (id) => {
  try {
    console.log('🔍 Fetching company type by ID:', id);

    const companyType = await sql`
      SELECT * FROM company_types WHERE id = ${id}
    `;

    if (companyType.length === 0) {
      throw new Error('Company type not found');
    }

    console.log('✅ Company type found:', companyType[0]);
    return companyType[0];

  } catch (error) {
    console.error('❌ Error fetching company type:', error);
    throw error;
  }
};

// Get company type statistics
export const getCompanyTypeStats = async () => {
  try {
    console.log('📊 Fetching company type statistics...');

    const stats = await sql`
      SELECT 
        COUNT(*) as total_types,
        COUNT(*) FILTER (WHERE "isActive" = true) as active_types,
        COUNT(*) FILTER (WHERE "isActive" = false) as inactive_types
      FROM company_types
    `;

    const usageStats = await sql`
      SELECT
        ct.name,
        ct.id,
        COUNT(v.id) as vendor_count,
        COUNT(c.id) as customer_count
      FROM company_types ct
      LEFT JOIN vendors v ON v.company_type = ct.name
      LEFT JOIN customers c ON c."companyType" = ct.name
      WHERE ct."isActive" = true
      GROUP BY ct.id, ct.name
      ORDER BY (COUNT(v.id) + COUNT(c.id)) DESC
    `;

    console.log('✅ Company type statistics fetched');
    return {
      overview: stats[0],
      usage: usageStats
    };

  } catch (error) {
    console.error('❌ Error fetching company type statistics:', error);
    throw error;
  }
};

// Update company type status
export const updateCompanyTypeStatus = async (id, status) => {
  try {
    console.log('🔄 Updating company type status:', { id, status });

    // Check table structure first
    const tableInfo = await checkCompanyTypesTable();
    if (!tableInfo.exists) {
      throw new Error('Company types table does not exist');
    }

    const columnNames = tableInfo.columns.map(col => col.column_name);
    const hasUpdatedAt = columnNames.includes('updated_at');
    const hasIsActive = columnNames.includes('isActive');
    const hasStatus = columnNames.includes('status');

    console.log('📋 Available columns for update:', columnNames);

    const isActive = status === 'Active';

    let updateQuery;
    if (hasIsActive && hasUpdatedAt) {
      updateQuery = sql`
        UPDATE company_types
        SET
          "isActive" = ${isActive},
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ${id}
        RETURNING *
      `;
    } else if (hasIsActive && !hasUpdatedAt) {
      updateQuery = sql`
        UPDATE company_types
        SET "isActive" = ${isActive}
        WHERE id = ${id}
        RETURNING *
      `;
    } else if (hasStatus && hasUpdatedAt) {
      updateQuery = sql`
        UPDATE company_types
        SET
          status = ${status},
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ${id}
        RETURNING *
      `;
    } else if (hasStatus && !hasUpdatedAt) {
      updateQuery = sql`
        UPDATE company_types
        SET status = ${status}
        WHERE id = ${id}
        RETURNING *
      `;
    } else {
      throw new Error('Table does not have isActive or status column');
    }

    const result = await updateQuery;

    if (result.length === 0) {
      throw new Error('Company type not found');
    }

    console.log('✅ Company type status updated successfully:', result[0]);
    return result[0];
  } catch (error) {
    console.error('❌ Error updating company type status:', error);
    throw error;
  }
};
