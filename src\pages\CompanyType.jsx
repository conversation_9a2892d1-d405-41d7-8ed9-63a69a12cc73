import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { PlusIcon, PencilIcon, TrashIcon, CheckCircleIcon, XCircleIcon, EyeIcon } from '@heroicons/react/24/outline';
import DataTable from '../components/DataTable/DataTable';

const CompanyType = () => {
  const navigate = useNavigate();
  const [showAddForm, setShowAddForm] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [companyTypeData, setCompanyTypeData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [editingId, setEditingId] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all'); // 'all', 'active', 'inactive'

  // Filter data based on status
  const getFilteredData = () => {
    switch (statusFilter) {
      case 'active':
        return companyTypeData.filter(item => item.status === 'Active' || item.isActive === true);
      case 'inactive':
        return companyTypeData.filter(item => item.status === 'Inactive' || item.isActive === false);
      default:
        return companyTypeData;
    }
  };

  // Transform database data to match expected format
  const transformCompanyTypeData = (data) => {
    return data.map(item => ({
      ...item,
      status: item.isActive !== undefined ? (item.isActive ? 'Active' : 'Inactive') : (item.status || 'Active'),
      createdDate: item.createdAt ? new Date(item.createdAt).toLocaleDateString() : (item.createdDate || new Date().toLocaleDateString()),
      updatedDate: item.updatedAt ? new Date(item.updatedAt).toLocaleDateString() : (item.updatedDate || null)
    }));
  };

  // Load company types from database
  const loadCompanyTypes = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading company types from database...');

      // First check if table exists
      const { checkCompanyTypesTable, getAllCompanyTypes } = await import('../services/companyTypeService');
      const tableInfo = await checkCompanyTypesTable();

      if (!tableInfo.exists) {
        console.log('❌ Table does not exist');
        setCompanyTypeData([]);
        alert('Company types table does not exist. Please click "Setup DB" to create it.');
        return;
      }

      console.log('✅ Table exists with columns:', tableInfo.columns.map(c => c.column_name));

      // Load data
      const dbCompanyTypes = await getAllCompanyTypes(true); // Include inactive
      console.log('✅ Company types loaded:', dbCompanyTypes);
      console.log('📊 Raw data type:', typeof dbCompanyTypes);
      console.log('📊 Raw data is array:', Array.isArray(dbCompanyTypes));

      // Ensure we have an array
      let dataArray = [];
      if (Array.isArray(dbCompanyTypes)) {
        dataArray = dbCompanyTypes;
      } else if (dbCompanyTypes && typeof dbCompanyTypes === 'object') {
        // Sometimes the result might be wrapped in an object
        if (dbCompanyTypes.rows) {
          dataArray = dbCompanyTypes.rows;
        } else if (dbCompanyTypes.data) {
          dataArray = dbCompanyTypes.data;
        } else {
          console.log('⚠️ Unexpected data format:', dbCompanyTypes);
          dataArray = [];
        }
      } else {
        console.log('⚠️ Data is not an array or object:', dbCompanyTypes);
        dataArray = [];
      }

      console.log('📊 Final data array:', dataArray);
      console.log('📊 Data array length:', dataArray.length);

      if (dataArray.length === 0) {
        console.log('⚠️ No data found in table');
        setCompanyTypeData([]);
        alert('No company types found. Please click "Setup DB" to add initial data.');
        return;
      }

      console.log('📊 Raw data sample:', dataArray.slice(0, 2));

      // Transform the data
      const transformedData = transformCompanyTypeData(dataArray);
      console.log('🔄 Transformed data sample:', transformedData.slice(0, 2));

      // Debug status counts
      const activeCount = transformedData.filter(item => item.status === 'Active' || item.isActive === true).length;
      const inactiveCount = transformedData.filter(item => item.status === 'Inactive' || item.isActive === false).length;
      console.log('📊 Status counts - Active:', activeCount, 'Inactive:', inactiveCount);

      setCompanyTypeData(transformedData);
    } catch (error) {
      console.error('❌ Error loading company types:', error);
      console.error('❌ Error details:', error.message);
      // Fallback to empty array if database fails
      setCompanyTypeData([]);
      alert(`Error loading data: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCompanyTypes();
  }, []);

  // Toggle status function
  const toggleStatus = async (id, currentStatus) => {
    console.log('🔄 Toggle status button clicked for:', { id, currentStatus });
    try {
      const newStatus = currentStatus === 'Active' ? 'Inactive' : 'Active';
      console.log(`🔄 Toggling status for ${id} from ${currentStatus} to ${newStatus}`);

      // Update in database using the status update function
      const { updateCompanyTypeStatus } = await import('../services/companyTypeService');
      const result = await updateCompanyTypeStatus(id, newStatus);
      console.log(`✅ Status updated successfully:`, result);

      // Reload the entire data to ensure consistency
      await loadCompanyTypes();
      alert(`✅ Status updated to ${newStatus} successfully!`);
    } catch (error) {
      console.error('❌ Error updating status:', error);
      alert(`❌ Error updating status: ${error.message || 'Please try again.'}`);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      alert('Please enter a company type name');
      return;
    }

    try {
      setLoading(true);
      
      if (editingId) {
        // Update existing company type
        const { updateCompanyType } = await import('../services/companyTypeService');
        await updateCompanyType(editingId, formData);
        alert('✅ Company type updated successfully!');
      } else {
        // Create new company type
        const { createCompanyType } = await import('../services/companyTypeService');
        await createCompanyType(formData);
        alert('✅ Company type created successfully!');
      }

      // Reset form and reload data
      setFormData({ name: '', description: '' });
      setEditingId(null);
      setShowAddForm(false);
      await loadCompanyTypes();
    } catch (error) {
      console.error('❌ Error saving company type:', error);
      alert(`❌ Error saving company type: ${error.message || 'Please try again.'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (companyType) => {
    console.log('🔧 Edit button clicked for:', companyType);
    setFormData({
      name: companyType.name,
      description: companyType.description || ''
    });
    setEditingId(companyType.id);
    setShowAddForm(true);
  };

  const handleDelete = async (companyType) => {
    console.log('🗑️ Delete button clicked for:', companyType);
    if (window.confirm(`Are you sure you want to delete "${companyType.name}"? This action cannot be undone.`)) {
      try {
        setLoading(true);
        const { deleteCompanyType } = await import('../services/companyTypeService');
        await deleteCompanyType(companyType.id);
        alert('✅ Company type deleted successfully!');
        await loadCompanyTypes();
      } catch (error) {
        console.error('❌ Error deleting company type:', error);
        alert(`❌ Error deleting company type: ${error.message || 'Please try again.'}`);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleCancel = () => {
    setFormData({ name: '', description: '' });
    setEditingId(null);
    setShowAddForm(false);
  };

  const columns = [
    {
      key: 'name',
      label: 'Company Type',
      sortable: true,
      render: (value) => (
        <div className="font-medium text-gray-900">{value}</div>
      )
    },
    {
      key: 'description',
      label: 'Description',
      sortable: false,
      render: (value) => (
        <div className="max-w-md text-gray-600 truncate" title={value}>
          {value}
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'Active'
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'createdDate',
      label: 'Created Date',
      sortable: true,
      render: (value) => (
        <span className="text-gray-600">{value}</span>
      )
    },
    {
      key: 'updatedDate',
      label: 'Updated Date',
      sortable: true,
      render: (value) => (
        <span className="text-gray-600">{value || 'N/A'}</span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      filterable: false,
      render: (_, row) => (
        <div className="flex space-x-2">
          <button
            onClick={() => {
              console.log('👁️ View button clicked for:', row);
              setFormData({
                name: row.name,
                description: row.description
              });
              setEditingId(row.id);
              setShowViewModal(true);
            }}
            className="text-gray-600 hover:text-gray-800"
            title="View"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleEdit(row)}
            className="text-blue-600 hover:text-blue-800"
            title="Edit"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => toggleStatus(row.id, row.status)}
            className={`${
              row.status === 'Active'
                ? 'text-green-600 hover:text-green-800'
                : 'text-gray-400 hover:text-gray-600'
            }`}
            title={`${row.status} - Click to toggle to ${row.status === 'Active' ? 'Inactive' : 'Active'}`}
          >
            {row.status === 'Active' ? (
              <CheckCircleIcon className="h-4 w-4" />
            ) : (
              <XCircleIcon className="h-4 w-4" />
            )}
          </button>
          <button
            onClick={() => handleDelete(row)}
            className="text-red-600 hover:text-red-800"
            title="Delete"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ];

  // Action Icons for DataTable Header
  const actionIcons = (
    <>
      <div className="flex flex-col items-center space-y-1">
        <EyeIcon className="w-4 h-4 text-gray-600" />
        <span className="text-xs text-gray-600">View</span>
      </div>
      <div className="flex flex-col items-center space-y-1">
        <PencilIcon className="w-4 h-4 text-blue-600" />
        <span className="text-xs text-blue-600">Edit</span>
      </div>
      <div className="flex flex-col items-center space-y-1">
        <CheckCircleIcon className="w-4 h-4 text-green-600" />
        <span className="text-xs text-green-600">Toggle Active</span>
      </div>
      <div className="flex flex-col items-center space-y-1">
        <XCircleIcon className="w-4 h-4 text-gray-400" />
        <span className="text-xs text-gray-400">Toggle Inactive</span>
      </div>
      <div className="flex flex-col items-center space-y-1">
        <TrashIcon className="w-4 h-4 text-red-600" />
        <span className="text-xs text-red-600">Delete</span>
      </div>
    </>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-gray-900">Company Type Management</h1>
          <p className="mt-1 sm:mt-2 text-sm text-gray-700">
            Manage company types for vendor and client classification
          </p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="btn-primary flex items-center justify-center space-x-2 w-full sm:w-auto"
        >
          <PlusIcon className="h-4 w-4" />
          <span className="text-sm sm:text-base">Add Company Type</span>
        </button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Total Types */}
        <div
          onClick={() => setStatusFilter('all')}
          className={`bg-white rounded-lg shadow-sm border p-4 sm:p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            statusFilter === 'all'
              ? 'border-blue-500 ring-2 ring-blue-200 bg-blue-50'
              : 'border-gray-200 hover:border-blue-300'
          }`}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-lg flex items-center justify-center ${
                statusFilter === 'all' ? 'bg-blue-200' : 'bg-blue-100'
              }`}>
                <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
            </div>
            <div className="ml-3 sm:ml-4">
              <p className="text-xs sm:text-sm font-medium text-gray-600">Total Types</p>
              <p className={`text-xl sm:text-2xl font-bold ${
                statusFilter === 'all' ? 'text-blue-700' : 'text-gray-900'
              }`}>{companyTypeData.length}</p>
            </div>
          </div>
          {statusFilter === 'all' && (
            <div className="mt-2 text-xs text-blue-600 font-medium">
              ✓ Showing all types
            </div>
          )}
        </div>

        {/* Active Types */}
        <div
          onClick={() => setStatusFilter('active')}
          className={`bg-white rounded-lg shadow-sm border p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            statusFilter === 'active'
              ? 'border-green-500 ring-2 ring-green-200 bg-green-50'
              : 'border-gray-200 hover:border-green-300'
          }`}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                statusFilter === 'active' ? 'bg-green-200' : 'bg-green-100'
              }`}>
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Types</p>
              <p className={`text-2xl font-bold ${
                statusFilter === 'active' ? 'text-green-700' : 'text-green-600'
              }`}>
                {companyTypeData.filter(item => item.status === 'Active').length}
              </p>
            </div>
          </div>
          {statusFilter === 'active' && (
            <div className="mt-2 text-xs text-green-600 font-medium">
              ✓ Showing active types only
            </div>
          )}
        </div>

        {/* Inactive Types */}
        <div
          onClick={() => setStatusFilter('inactive')}
          className={`bg-white rounded-lg shadow-sm border p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            statusFilter === 'inactive'
              ? 'border-red-500 ring-2 ring-red-200 bg-red-50'
              : 'border-gray-200 hover:border-red-300'
          }`}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                statusFilter === 'inactive' ? 'bg-red-200' : 'bg-red-100'
              }`}>
                <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Inactive Types</p>
              <p className={`text-2xl font-bold ${
                statusFilter === 'inactive' ? 'text-red-700' : 'text-red-600'
              }`}>
                {companyTypeData.filter(item =>
                  item.status === 'Inactive' || item.isActive === false
                ).length}
              </p>
            </div>
          </div>
          {statusFilter === 'inactive' && (
            <div className="mt-2 text-xs text-red-600 font-medium">
              ✓ Showing inactive types only
            </div>
          )}
        </div>
      </div>

      {/* Filter Status Indicator */}
      {statusFilter !== 'all' && (
        <div className="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
            </svg>
            <span className="text-sm font-medium text-blue-800">
              Showing {statusFilter} types only ({getFilteredData().length} of {companyTypeData.length} total)
            </span>
          </div>
          <button
            onClick={() => setStatusFilter('all')}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            Clear Filter
          </button>
        </div>
      )}

      {/* Data Table */}
      <div className="card">
        <DataTable
          data={getFilteredData()}
          columns={columns}
          title="Company Types"
          defaultPageSize={10}
          pageSizeOptions={[10, 20, 50, 100]}
          enableExport={true}
          enableColumnToggle={true}
          enableFiltering={true}
          enableSorting={true}
          loading={loading}
          actionIcons={actionIcons}
        />
      </div>

      {/* Add/Edit Form Modal */}
      {showAddForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={handleCancel}></div>
            <div className="relative bg-white rounded-lg max-w-md w-full p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">
                  {editingId ? 'Edit Company Type' : 'Add New Company Type'}
                </h3>
                <button
                  onClick={handleCancel}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Company Type Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter company type name"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter description (optional)"
                  />
                </div>
                <div className="flex space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {loading ? 'Saving...' : (editingId ? 'Update' : 'Create')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* View Modal */}
      {showViewModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => setShowViewModal(false)}></div>
            <div className="relative bg-white rounded-lg max-w-md w-full p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">Company Type Details</h3>
                <button
                  onClick={() => setShowViewModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Company Type Name</label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                    {formData.name}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 min-h-[80px]">
                    {formData.description || 'No description provided'}
                  </div>
                </div>
              </div>

              <div className="flex justify-end pt-6">
                <button
                  onClick={() => setShowViewModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompanyType;
