import { sql } from '../config/database.js';
import { processVendorFiles } from './fileUploadService.js';
import { createUserAccount, sendLoginCredentials } from '../utils/createAuthenticationSystem.js';

// Get all vendors
export const getAllVendors = async () => {
  try {
    const vendors = await sql`
      SELECT
        id,
        company_name,
        company_type,
        onboarding_date,
        emails,
        phones,
        address,
        country,
        state,
        city,
        username,
        gst_number,
        description,
        services,
        website,
        type_of_work,
        status,
        files,
        rating,
        total_orders,
        created_at,
        updated_at
      FROM vendors
      ORDER BY created_at DESC
    `;

    // Transform data to match UI expectations and format dates
    return vendors.map(vendor => {
      // Parse JSON fields safely
      let emails = [];
      let phones = [];
      let services = [];
      let files = {};

      try {
        emails = vendor.emails ? (typeof vendor.emails === 'string' ? JSON.parse(vendor.emails) : vendor.emails) : [];
      } catch (e) {
        console.warn('Error parsing emails:', e);
        emails = [];
      }

      try {
        phones = vendor.phones ? (typeof vendor.phones === 'string' ? JSON.parse(vendor.phones) : vendor.phones) : [];
      } catch (e) {
        console.warn('Error parsing phones:', e);
        phones = [];
      }

      try {
        services = vendor.services ? (typeof vendor.services === 'string' ? JSON.parse(vendor.services) : vendor.services) : [];
      } catch (e) {
        console.warn('Error parsing services:', e);
        services = [];
      }

      try {
        files = vendor.files ? (typeof vendor.files === 'string' ? JSON.parse(vendor.files) : vendor.files) : {};
      } catch (e) {
        console.warn('Error parsing files:', e);
        files = {};
      }

      return {
        id: vendor.id,
        // Keep both formats for compatibility
        companyName: vendor.company_name || '',
        company_name: vendor.company_name || '',
        companyType: vendor.company_type || 'Company',
        company_type: vendor.company_type || 'Company',
        onboardingDate: vendor.onboarding_date ? new Date(vendor.onboarding_date).toISOString().split('T')[0] : '',
        emails: Array.isArray(emails) ? emails : [],
        phones: Array.isArray(phones) ? phones : [],
        email: Array.isArray(emails) && emails.length > 0 ? emails[0] : '',
        phone: Array.isArray(phones) && phones.length > 0 ? phones[0] : '',
        address: vendor.address || '',
        country: vendor.country || '',
        state: vendor.state || '',
        city: vendor.city || '',
        username: vendor.username || '',
        gstNumber: vendor.gst_number || '',
        description: vendor.description || '',
        services: Array.isArray(services) ? services : [],
        website: vendor.website || '',
        typeOfWork: (() => {
          try {
            return Array.isArray(vendor.type_of_work)
              ? vendor.type_of_work
              : vendor.type_of_work
                ? JSON.parse(vendor.type_of_work)
                : [];
          } catch {
            return vendor.type_of_work ? [vendor.type_of_work] : [];
          }
        })(),
        type_of_work: vendor.type_of_work || '',
        pointOfContact: (() => {
          try {
            return Array.isArray(vendor.point_of_contact)
              ? vendor.point_of_contact
              : vendor.point_of_contact
                ? JSON.parse(vendor.point_of_contact)
                : [];
          } catch {
            return [];
          }
        })(),
        status: vendor.status || 'Pending',
        startupBenefits: vendor.startup_benefits || 'No',
        files: files,
        rating: parseFloat(vendor.rating) || 0,
        totalOrders: vendor.total_orders || 0,
        total_orders: vendor.total_orders || 0,
        createdAt: vendor.created_at ? new Date(vendor.created_at).toISOString().split('T')[0] : '',
        updatedAt: vendor.updated_at ? new Date(vendor.updated_at).toISOString().split('T')[0] : ''
      };
    });
  } catch (error) {
    console.error('Error fetching vendors:', error);
    throw error;
  }
};




// Get vendor by ID
export const getVendorById = async (id) => {
  try {
    // First try to get from database using correct schema
    const vendor = await sql`
      SELECT
        id,
        company_name,
        company_type,
        onboarding_date,
        emails,
        phones,
        address,
        country,
        state,
        city,
        username,
        gst_number,
        description,
        services,
        website,
        type_of_work,
        status,
        files,
        rating,
        total_orders,
        created_at,
        updated_at
      FROM vendors
      WHERE id = ${id}
    `;

    const vendorData = vendor[0];

    // If found in database, transform and return it
    if (vendorData) {
      console.log('✅ Vendor found in database:', vendorData);

      // Transform the data to match form expectations
      let emails = [];
      let phones = [];
      let services = [];
      let files = {};

      try {
        emails = vendorData.emails ? (typeof vendorData.emails === 'string' ? JSON.parse(vendorData.emails) : vendorData.emails) : [];
      } catch (e) {
        emails = [];
      }

      try {
        phones = vendorData.phones ? (typeof vendorData.phones === 'string' ? JSON.parse(vendorData.phones) : vendorData.phones) : [];
      } catch (e) {
        phones = [];
      }

      try {
        services = vendorData.services ? (typeof vendorData.services === 'string' ? JSON.parse(vendorData.services) : vendorData.services) : [];
      } catch (e) {
        services = [];
      }

      try {
        files = vendorData.files ? (typeof vendorData.files === 'string' ? JSON.parse(vendorData.files) : vendorData.files) : {};
      } catch (e) {
        files = {};
      }

      return {
        id: vendorData.id,
        companyName: vendorData.company_name || '',
        companyType: vendorData.company_type || 'Company',
        onboardingDate: vendorData.onboarding_date ? new Date(vendorData.onboarding_date).toISOString().split('T')[0] : '',
        emails: Array.isArray(emails) ? emails : [],
        phones: Array.isArray(phones) ? phones : [],
        address: vendorData.address || '',
        country: vendorData.country || '',
        state: vendorData.state || '',
        city: vendorData.city || '',
        username: vendorData.username || '',
        gstNumber: vendorData.gst_number || '',
        description: vendorData.description || '',
        services: Array.isArray(services) ? services : [],
        website: vendorData.website || '',
        typeOfWork: (() => {
          try {
            return Array.isArray(vendorData.type_of_work)
              ? vendorData.type_of_work
              : vendorData.type_of_work
                ? JSON.parse(vendorData.type_of_work)
                : [];
          } catch {
            return vendorData.type_of_work ? [vendorData.type_of_work] : [];
          }
        })(),
        pointOfContact: (() => {
          try {
            return Array.isArray(vendorData.point_of_contact)
              ? vendorData.point_of_contact
              : vendorData.point_of_contact
                ? JSON.parse(vendorData.point_of_contact)
                : [];
          } catch {
            return [];
          }
        })(),
        status: vendorData.status || 'Pending',
        startupBenefits: vendorData.startup_benefits || 'No',
        files: files,
        rating: parseFloat(vendorData.rating) || 0,
        totalOrders: vendorData.total_orders || 0,
        createdAt: vendorData.created_at,
        updatedAt: vendorData.updated_at
      };
    }

    console.log('❌ Vendor not found:', id);
    return null;
  } catch (error) {
    console.error('Error fetching vendor by ID:', error);
    throw error;
  }
};

// Create new vendor
export const createVendor = async (vendorData) => {
  try {
    console.log('🔄 Creating vendor with data:', vendorData);

    const {
      companyName,
      companyType,
      onboardingDate,
      emails,
      phones,
      address,
      country,
      state,
      city,
      username,
      gstNumber,
      description,
      services,
      website,
      typeOfWork,
      status = 'Pending',
      files = {},
      pointOfContact = [],
      startupBenefits = 'No'
    } = vendorData;

    // Process uploaded files - Handle S3 files properly
    let processedFiles = {
      gstFileUrl: null,
      ndaFileUrl: null,
      agreementFileUrl: null,
      companyLogoUrl: null,
      companyLogos: [],
      otherDocsUrls: []
    };

    try {
      if (files && Object.keys(files).length > 0) {
        console.log('📁 Processing vendor files...');

        // Handle S3 uploaded files
        if (files.gstFile) {
          processedFiles.gstFileUrl = files.gstFile.url || files.gstFile;
        }
        if (files.ndaFile) {
          processedFiles.ndaFileUrl = files.ndaFile.url || files.ndaFile;
        }
        if (files.agreementFile) {
          processedFiles.agreementFileUrl = files.agreementFile.url || files.agreementFile;
        }
        if (files.companyLogos && Array.isArray(files.companyLogos)) {
          processedFiles.companyLogos = files.companyLogos;
          if (files.companyLogos.length > 0) {
            processedFiles.companyLogoUrl = files.companyLogos[0].url || files.companyLogos[0];
          }
        }

        console.log('✅ Files processed:', processedFiles);
      }
    } catch (fileError) {
      console.warn('⚠️ File processing failed, continuing without files:', fileError);
    }

    // Prepare data for database insertion using actual schema
    console.log('📋 Form data breakdown:');
    console.log('  - Company Name:', companyName);
    console.log('  - Company Type:', companyType);
    console.log('  - Onboarding Date:', onboardingDate);
    console.log('  - Emails:', emails);
    console.log('  - Phones:', phones);
    console.log('  - Address:', address);
    console.log('  - Country:', country);
    console.log('  - State:', state);
    console.log('  - City:', city);
    console.log('  - Username:', username);
    console.log('  - GST Number:', gstNumber);
    console.log('  - Description:', description);
    console.log('  - Services:', services);
    console.log('  - Website:', website);
    console.log('  - Type of Work:', typeOfWork);
    console.log('  - Status:', status);
    console.log('  - Point of Contact:', pointOfContact);
    console.log('  - Startup Benefits:', startupBenefits);
    console.log('  - Files:', processedFiles);

    const vendor = await sql`
      INSERT INTO vendors (
        company_name,
        company_type,
        onboarding_date,
        emails,
        phones,
        address,
        country,
        state,
        city,
        username,
        gst_number,
        description,
        services,
        website,
        type_of_work,
        status,
        files,
        point_of_contact,
        startup_benefits,
        rating,
        total_orders
      ) VALUES (
        ${companyName || ''},
        ${companyType || null},
        ${onboardingDate ? new Date(onboardingDate) : null},
        ${JSON.stringify(emails || [])},
        ${JSON.stringify(phones || [])},
        ${address || null},
        ${country || null},
        ${state || null},
        ${city || null},
        ${username || null},
        ${gstNumber || null},
        ${description || null},
        ${JSON.stringify(Array.isArray(services) ? services : (services ? [services] : []))},
        ${website || null},
        ${JSON.stringify(Array.isArray(typeOfWork) ? typeOfWork : (typeOfWork ? [typeOfWork] : []))},
        ${status || 'Pending'},
        ${JSON.stringify(processedFiles)},
        ${JSON.stringify(Array.isArray(pointOfContact) ? pointOfContact : [])},
        ${startupBenefits || 'No'},
        ${0},
        ${0}
      )
      RETURNING *
    `;

    console.log('✅ Vendor created successfully:', vendor[0]);

    // Create user account for vendor login
    try {
      console.log('🔐 Creating user account for vendor...');

      const userAccountData = {
        username: username || `vendor_${vendor[0].id}`,
        email: Array.isArray(emails) && emails.length > 0 ? emails[0] : email,
        userType: 'vendor',
        userId: vendor[0].id
      };

      const userResult = await createUserAccount(userAccountData);

      if (userResult.success) {
        console.log('✅ User account created for vendor:', userResult.user.username);

        // Send login credentials via email
        const emailResult = await sendLoginCredentials(
          userResult.user.email,
          userResult.user.username,
          userResult.password,
          'vendor'
        );

        if (emailResult.success) {
          console.log('✅ Login credentials sent to vendor email');
        } else {
          console.warn('⚠️ Failed to send login credentials email:', emailResult.error);
        }

        // Add user account info to vendor response
        vendor[0].userAccount = {
          username: userResult.user.username,
          email: userResult.user.email,
          credentialsSent: emailResult.success
        };
      } else {
        console.warn('⚠️ Failed to create user account for vendor:', userResult.error);
      }
    } catch (authError) {
      console.warn('⚠️ Authentication setup failed for vendor:', authError);
      // Don't fail vendor creation if auth setup fails
    }

    return vendor[0];
  } catch (error) {
    console.error('❌ Error creating vendor:', error);
    throw error;
  }
};

// Update vendor
export const updateVendor = async (id, vendorData) => {
  try {
    console.log('🔄 Updating vendor:', id);
    console.log('📋 Update data:', vendorData);

    // Handle files for update - files are already processed S3 files
    let updatedFiles = {};
    if (vendorData.files && Object.keys(vendorData.files).length > 0) {
      console.log('📁 Processing vendor files for update...');

      // Get current vendor to merge with existing files
      const currentVendor = await getVendorById(id);
      const existingFiles = currentVendor?.files || {};

      // Build updated files object
      updatedFiles = {
        gstFileUrl: null,
        ndaFileUrl: null,
        agreementFileUrl: null,
        companyLogoUrl: null,
        companyLogos: [],
        otherDocsUrls: []
      };

      // Preserve existing files and update with new ones
      if (existingFiles.gstFileUrl) updatedFiles.gstFileUrl = existingFiles.gstFileUrl;
      if (existingFiles.ndaFileUrl) updatedFiles.ndaFileUrl = existingFiles.ndaFileUrl;
      if (existingFiles.agreementFileUrl) updatedFiles.agreementFileUrl = existingFiles.agreementFileUrl;
      if (existingFiles.companyLogoUrl) updatedFiles.companyLogoUrl = existingFiles.companyLogoUrl;
      if (existingFiles.companyLogos) updatedFiles.companyLogos = existingFiles.companyLogos;
      if (existingFiles.otherDocsUrls) updatedFiles.otherDocsUrls = existingFiles.otherDocsUrls;

      // Update with new files from S3FileUpload
      if (vendorData.files.gstFile) {
        updatedFiles.gstFileUrl = vendorData.files.gstFile.url;
      }
      if (vendorData.files.ndaFile) {
        updatedFiles.ndaFileUrl = vendorData.files.ndaFile.url;
      }
      if (vendorData.files.agreementFile) {
        updatedFiles.agreementFileUrl = vendorData.files.agreementFile.url;
      }

      console.log('✅ Files updated for vendor:', updatedFiles);
    }

    // Build update object with only the fields that exist in the database
    const updateObj = {};

    if (vendorData.companyName !== undefined) {
      updateObj.company_name = vendorData.companyName;
    }

    if (vendorData.companyType !== undefined) {
      updateObj.company_type = vendorData.companyType;
    }

    if (vendorData.onboardingDate !== undefined) {
      updateObj.onboarding_date = vendorData.onboardingDate;
    }

    if (vendorData.emails !== undefined && Array.isArray(vendorData.emails)) {
      updateObj.emails = JSON.stringify(vendorData.emails.filter(email => email.trim() !== ''));
    } else if (vendorData.email !== undefined) {
      updateObj.emails = JSON.stringify([vendorData.email]);
    }

    if (vendorData.phones !== undefined && Array.isArray(vendorData.phones)) {
      updateObj.phones = JSON.stringify(vendorData.phones.filter(phone => phone.trim() !== ''));
    } else if (vendorData.phone !== undefined) {
      updateObj.phones = JSON.stringify([vendorData.phone]);
    }

    if (vendorData.address !== undefined) {
      updateObj.address = vendorData.address;
    }

    if (vendorData.country !== undefined) {
      updateObj.country = vendorData.country;
    }

    if (vendorData.state !== undefined) {
      updateObj.state = vendorData.state;
    }

    if (vendorData.city !== undefined) {
      updateObj.city = vendorData.city;
    }

    if (vendorData.username !== undefined) {
      updateObj.username = vendorData.username;
    }

    if (vendorData.gstNumber !== undefined) {
      updateObj.gst_number = vendorData.gstNumber;
    }

    if (vendorData.rating !== undefined) {
      updateObj.rating = vendorData.rating;
    }

    if (vendorData.typeOfWork !== undefined) {
      updateObj.type_of_work = JSON.stringify(Array.isArray(vendorData.typeOfWork) ? vendorData.typeOfWork : (vendorData.typeOfWork ? [vendorData.typeOfWork] : []));
    }

    if (vendorData.status !== undefined) {
      updateObj.status = vendorData.status;
    } else if (vendorData.isActive !== undefined) {
      updateObj.status = vendorData.isActive ? 'Active' : 'Pending';
    }

    if (vendorData.description !== undefined) {
      updateObj.description = vendorData.description;
    }

    if (vendorData.website !== undefined) {
      updateObj.website = vendorData.website;
    }

    if (vendorData.services !== undefined && Array.isArray(vendorData.services)) {
      updateObj.services = JSON.stringify(vendorData.services);
    }

    if (vendorData.pointOfContact !== undefined) {
      updateObj.point_of_contact = JSON.stringify(Array.isArray(vendorData.pointOfContact) ? vendorData.pointOfContact : []);
    }

    if (vendorData.startupBenefits !== undefined) {
      updateObj.startup_benefits = vendorData.startupBenefits || 'No';
    }

    if (vendorData.files !== undefined || Object.keys(updatedFiles).length > 0) {
      // Use the updated files object
      updateObj.files = JSON.stringify(updatedFiles);
    }

    if (vendorData.totalOrders !== undefined) {
      updateObj.total_orders = vendorData.totalOrders;
    }

    // Always update the updated_at field
    updateObj.updated_at = new Date();

    // Check if we have any fields to update
    if (Object.keys(updateObj).length === 1) { // Only updatedAt
      return await getVendorById(id);
    }

    // Perform the update using a simple approach
    const vendor = await sql`
      UPDATE vendors SET
        company_name = ${updateObj.company_name || sql`company_name`},
        company_type = ${updateObj.company_type || sql`company_type`},
        onboarding_date = ${updateObj.onboarding_date || sql`onboarding_date`},
        emails = ${updateObj.emails || sql`emails`},
        phones = ${updateObj.phones || sql`phones`},
        address = ${updateObj.address || sql`address`},
        country = ${updateObj.country || sql`country`},
        state = ${updateObj.state || sql`state`},
        city = ${updateObj.city || sql`city`},
        username = ${updateObj.username || sql`username`},
        gst_number = ${updateObj.gst_number || sql`gst_number`},
        description = ${updateObj.description || sql`description`},
        services = ${updateObj.services || sql`services`},
        website = ${updateObj.website || sql`website`},
        type_of_work = ${updateObj.type_of_work || sql`type_of_work`},
        status = ${updateObj.status || sql`status`},
        files = ${updateObj.files || sql`files`},
        rating = ${updateObj.rating || sql`rating`},
        total_orders = ${updateObj.total_orders || sql`total_orders`},
        updated_at = NOW()
      WHERE id = ${id}
      RETURNING *
    `;

    if (!vendor || vendor.length === 0) {
      return await getVendorById(id);
    }

    return vendor[0];
  } catch (error) {
    console.error('Error updating vendor:', error);
    throw error;
  }
};

// Delete vendor
export const deleteVendor = async (id) => {
  try {
    await sql`DELETE FROM vendors WHERE id = ${id}`;
    return true;
  } catch (error) {
    console.error('Error deleting vendor:', error);
    throw error;
  }
};

// Get vendor statistics
export const getVendorStats = async () => {
  try {
    const stats = await sql`
      SELECT
        COUNT(*) as total_vendors,
        COUNT(CASE WHEN "isActive" = true THEN 1 END) as active_vendors,
        COUNT(CASE WHEN "isActive" = false THEN 1 END) as inactive_vendors,
        AVG(rating) as average_rating
      FROM vendors
    `;
    return stats[0];
  } catch (error) {
    console.error('Error fetching vendor stats:', error);
    throw error;
  }
};

// Search vendors
export const searchVendors = async (searchTerm) => {
  try {
    const vendors = await sql`
      SELECT
        id,
        name,
        company,
        "companyName",
        "companyType",
        "onboardingDate",
        email,
        phone,
        address,
        country,
        state,
        city,
        username,
        "gstNumber",
        specialization,
        "typeOfWork",
        "isActive",
        rating,
        "createdAt",
        "updatedAt"
      FROM vendors
      WHERE
        "companyName" ILIKE ${`%${searchTerm}%`} OR
        username ILIKE ${`%${searchTerm}%`} OR
        "gstNumber" ILIKE ${`%${searchTerm}%`} OR
        specialization ILIKE ${`%${searchTerm}%`}
      ORDER BY "createdAt" DESC
    `;
    return vendors;
  } catch (error) {
    console.error('Error searching vendors:', error);
    throw error;
  }
};
