import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { isAuthenticated } from '../services/enhancedAuthService';

const Login = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Prevent access to login page if already authenticated
  useEffect(() => {
    if (isAuthenticated()) {
      console.log('🔒 Login: User already authenticated, redirecting to dashboard');
      navigate('/dashboard', { replace: true });
    }
  }, [navigate]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      console.log('🔐 Attempting login:', { username: formData.username, password: formData.password });

      // Import and use enhanced auth service
      const { login } = await import('../services/enhancedAuthService');
      console.log('🔐 Login function imported successfully');

      const result = await login({
        username: formData.username,
        password: formData.password
      });

      console.log('🔐 Login result:', result);

      if (result.success) {
        console.log('✅ Login successful:', result.user);

        // Check if password change is required
        if (result.mustChangePassword) {
          // TODO: Redirect to password change page
          console.log('⚠️ Password change required');
          setError('Password change required on first login. Feature coming soon.');
          return;
        }

        // Navigate based on user type
        const dashboardUrl = result.user.userType === 'vendor' ? '/vendor-dashboard' : '/dashboard';
        console.log('🔐 Navigating to:', dashboardUrl);
        navigate(dashboardUrl);
      } else {
        console.log('❌ Login failed:', result.error);
        setError(result.error || 'Invalid username or password');
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      setError('Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="min-h-screen flex">
        {/* Left Side - Logo Section */}
        <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-indigo-700 items-center justify-center p-12">
          <div className="text-center text-white">
            <div className="mb-8">
              <img
                src="/innoventorysologo.png"
                alt="Innoventory Logo"
                className="h-32 w-auto mx-auto mb-6 bg-white rounded-xl p-4 shadow-2xl"
                onError={(e) => {
                  // Fallback to text if image fails to load
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
              <div className="hidden h-32 w-32 bg-white rounded-xl items-center justify-center mx-auto mb-6 shadow-2xl">
                <span className="text-blue-600 font-bold text-4xl">I</span>
              </div>
            </div>
            <h1 className="text-4xl font-bold mb-4">
              Innoventory Solutions
            </h1>
            <p className="text-xl text-blue-100 mb-8">
              Admin Panel
            </p>
            <div className="text-blue-200">
              <p className="mb-2">Streamline your business operations</p>
              <p>Manage vendors, clients, and orders efficiently</p>
            </div>
          </div>
        </div>

        {/* Right Side - Login Form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="max-w-md w-full space-y-8">
            {/* Mobile Logo - Only visible on small screens */}
            <div className="lg:hidden text-center mb-8">
              <div className="mx-auto h-20 w-20 bg-white rounded-xl flex items-center justify-center shadow-lg p-2 mb-4">
                <img
                  src="/innoventorysologo.png"
                  alt="Innoventory Logo"
                  className="h-full w-full object-contain"
                  onError={(e) => {
                    // Fallback to text if image fails to load
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'flex';
                  }}
                />
                <div className="hidden h-16 w-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl items-center justify-center">
                  <span className="text-white font-bold text-2xl">I</span>
                </div>
              </div>
              <h2 className="text-2xl font-bold text-gray-900">
                Innoventory Solutions
              </h2>
              <p className="text-sm text-gray-600 mt-2">
                Admin Panel
              </p>
            </div>

            {/* Form Header */}
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Welcome Back
              </h2>
              <p className="text-gray-600">
                Sign in to your admin account
              </p>
            </div>

            {/* Login Form */}
            <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="bg-white rounded-xl shadow-lg p-8 space-y-6">
            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Username Field */}
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
                Username
              </label>
              <input
                id="username"
                name="username"
                type="text"
                autoComplete="username"
                required
                value={formData.username}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="Enter your username"
              />
              <p className="mt-1 text-xs text-gray-500">
                Use 'admin' for administrator access, or your assigned username for vendor/sub-admin access
              </p>
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-5 w-5" />
                  ) : (
                    <EyeIcon className="h-5 w-5" />
                  )}
                </button>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Signing in...
                </div>
              ) : (
                'Sign In'
              )}
            </button>
          </div>

          {/* Demo Credentials */}
          <div className="text-center mt-4">
            <p className="text-xs text-gray-500">
              Demo: admin / admin123
            </p>
          </div>
        </form>

        {/* Footer */}
        <div className="text-center mt-8">
              <p className="text-xs text-gray-500">
                © 2025 Innoventory. All rights reserved. Developing by Wipster Technologies Private Limited
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
