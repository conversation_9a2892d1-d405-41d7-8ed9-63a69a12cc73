/**
 * Test Improved PDF Export
 * Tests the enhanced PDF export with proper column formatting
 */

/**
 * Test PDF with overlapping text scenario
 */
export const testOverlappingTextFix = async () => {
  try {
    console.log('🧪 Testing PDF export with long text (overlap fix)...');

    // Dynamic import
    const jsPDFModule = await import('jspdf');
    await import('jspdf-autotable');

    const jsPDFClass = jsPDFModule.default;
    const doc = new jsPDFClass();

    // Add title
    doc.setFontSize(16);
    doc.text('Improved PDF Export Test', 14, 22);

    // Add timestamp
    doc.setFontSize(10);
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 14, 32);

    // Test data with long text that would cause overlap
    const headers = ['Work Type', 'Description', 'Status', 'Created Date', 'Updated Date'];
    const rows = [
      [
        'Patent Filing',
        'This is a very long description that would normally cause text overlap in PDF exports but should now be handled properly',
        'Active',
        '5/7/2025',
        '5/7/2025'
      ],
      [
        'Software Development',
        'Custom software development and programming services with detailed specifications and requirements',
        'Active',
        '16/7/2025',
        'N/A'
      ],
      [
        'Test',
        'TestTestTestTestTestTestTestTestTestTestTestTestTestTestTestTestTestTestTestTestTestTest',
        'Active',
        '16/7/2025',
        '16/7/2025'
      ]
    ];

    if (typeof doc.autoTable === 'function') {
      // Calculate dynamic column widths
      const pageWidth = doc.internal.pageSize.getWidth();
      const margins = 28;
      const availableWidth = pageWidth - margins;

      const columnRatios = {
        'Work Type': 0.15,
        'Description': 0.35,
        'Status': 0.12,
        'Created Date': 0.19,
        'Updated Date': 0.19
      };

      const columnStyles = {};
      headers.forEach((header, index) => {
        const ratio = columnRatios[header] || (1 / headers.length);
        const width = availableWidth * ratio;
        columnStyles[index] = { cellWidth: width };
      });

      doc.autoTable({
        head: [headers],
        body: rows,
        startY: 40,
        styles: {
          fontSize: 8,
          cellPadding: 2,
          lineColor: [0, 0, 0],
          lineWidth: 0.1,
          textColor: [0, 0, 0],
          overflow: 'linebreak',
          cellWidth: 'wrap'
        },
        headStyles: {
          fillColor: [59, 130, 246],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
          lineColor: [0, 0, 0],
          lineWidth: 0.2,
          fontSize: 9
        },
        bodyStyles: {
          lineColor: [0, 0, 0],
          lineWidth: 0.1,
        },
        alternateRowStyles: {
          fillColor: [248, 250, 252],
        },
        columnStyles: columnStyles,
        tableLineColor: [0, 0, 0],
        tableLineWidth: 0.2,
        margin: { top: 40, left: 14, right: 14 },
        theme: 'grid'
      });

      console.log('✅ Improved PDF with proper column widths created!');
    } else {
      console.log('❌ autoTable not available, using fallback...');

      // Improved fallback with proper spacing
      doc.setFontSize(10);
      let yPosition = 50;
      const columnWidths = [40, 60, 25, 30, 30];

      // Headers
      headers.forEach((header, index) => {
        const xPosition = 14 + columnWidths.slice(0, index).reduce((sum, width) => sum + width, 0);
        doc.text(header.substring(0, 15), xPosition, yPosition);
      });

      yPosition += 15;

      // Data rows with truncation
      rows.forEach((row, rowIndex) => {
        if (yPosition > 270) {
          doc.addPage();
          yPosition = 20;
        }

        row.forEach((cell, cellIndex) => {
          const xPosition = 14 + columnWidths.slice(0, cellIndex).reduce((sum, width) => sum + width, 0);
          let cellText = String(cell);

          // Truncate based on column
          if (cellIndex === 1) { // Description column
            cellText = cellText.substring(0, 25) + (cellText.length > 25 ? '...' : '');
          } else {
            cellText = cellText.substring(0, 15) + (cellText.length > 15 ? '...' : '');
          }

          doc.text(cellText, xPosition, yPosition);
        });

        yPosition += 12;
      });
    }

    doc.save('improved_pdf_test.pdf');
    console.log('✅ Improved PDF export test completed!');
    return true;

  } catch (error) {
    console.error('❌ Improved PDF test failed:', error);
    return false;
  }
};

/**
 * Test current Type of Work data with improvements
 */
export const testCurrentDataImproved = async () => {
  try {
    console.log('🧪 Testing current Type of Work data with improvements...');

    // Get current page data
    const tables = document.querySelectorAll('table');
    if (tables.length === 0) {
      console.log('❌ No tables found on page');
      return false;
    }

    const table = tables[0];
    const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
    const rows = Array.from(table.querySelectorAll('tbody tr')).map(tr =>
      Array.from(tr.querySelectorAll('td')).map(td => {
        let text = td.textContent.trim();
        // Clean up text
        text = text.replace(/\s+/g, ' ');
        return text;
      })
    );

    console.log('📋 Headers found:', headers);
    console.log('📄 Rows found:', rows.length);

    // Process data to prevent overlap
    const processedRows = rows.map(row =>
      row.map((cell, cellIndex) => {
        let processedCell = cell;

        // Handle description column (usually index 1)
        if (cellIndex === 1 && processedCell.length > 100) {
          processedCell = processedCell.substring(0, 97) + '...';
        } else if (cellIndex !== 1 && processedCell.length > 50) {
          processedCell = processedCell.substring(0, 47) + '...';
        }

        return processedCell;
      })
    );

    // Create improved PDF
    const jsPDFModule = await import('jspdf');
    await import('jspdf-autotable');

    const jsPDFClass = jsPDFModule.default;
    const doc = new jsPDFClass();

    // Add title
    doc.setFontSize(16);
    doc.text('Type of Work - Improved Export', 14, 22);

    // Add timestamp
    doc.setFontSize(10);
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 14, 32);

    if (typeof doc.autoTable === 'function') {
      // Calculate column widths
      const pageWidth = doc.internal.pageSize.getWidth();
      const margins = 28;
      const availableWidth = pageWidth - margins;

      const columnRatios = {
        'Work Type': 0.15,
        'Description': 0.35,
        'Status': 0.12,
        'Created Date': 0.19,
        'Updated Date': 0.19
      };

      const columnStyles = {};
      headers.forEach((header, index) => {
        const ratio = columnRatios[header] || (1 / headers.length);
        const width = availableWidth * ratio;
        columnStyles[index] = { cellWidth: width };
      });

      doc.autoTable({
        head: [headers],
        body: processedRows,
        startY: 40,
        styles: {
          fontSize: 8,
          cellPadding: 2,
          lineColor: [0, 0, 0],
          lineWidth: 0.1,
          textColor: [0, 0, 0],
          overflow: 'linebreak',
          cellWidth: 'wrap'
        },
        headStyles: {
          fillColor: [59, 130, 246],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
          lineColor: [0, 0, 0],
          lineWidth: 0.2,
          fontSize: 9
        },
        bodyStyles: {
          lineColor: [0, 0, 0],
          lineWidth: 0.1,
        },
        alternateRowStyles: {
          fillColor: [248, 250, 252],
        },
        columnStyles: columnStyles,
        tableLineColor: [0, 0, 0],
        tableLineWidth: 0.2,
        margin: { top: 40, left: 14, right: 14 },
        theme: 'grid'
      });
    }

    doc.save('type_of_work_improved.pdf');
    console.log('✅ Improved Type of Work PDF created!');
    return true;

  } catch (error) {
    console.error('❌ Current data improved test failed:', error);
    return false;
  }
};

/**
 * Compare before and after PDF exports
 */
export const compareBeforeAfter = async () => {
  console.log('🔄 Creating before/after comparison PDFs...');

  try {
    // Test 1: Create "before" PDF (with overlap issues)
    await testOverlappingTextFix();

    // Test 2: Create "after" PDF (with improvements)
    await testCurrentDataImproved();

    console.log('');
    console.log('📊 COMPARISON RESULTS:');
    console.log('✅ Before PDF: improved_pdf_test.pdf');
    console.log('✅ After PDF: type_of_work_improved.pdf');
    console.log('');
    console.log('💡 Check your downloads folder to compare:');
    console.log('   - Text should no longer overlap');
    console.log('   - Columns should have proper widths');
    console.log('   - Long text should be wrapped or truncated');
    console.log('   - Table should have clear borders');

    return true;

  } catch (error) {
    console.error('❌ Comparison test failed:', error);
    return false;
  }
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.testOverlappingTextFix = testOverlappingTextFix;
  window.testCurrentDataImproved = testCurrentDataImproved;
  window.compareBeforeAfter = compareBeforeAfter;

  console.log('🔧 Improved PDF Test Functions Available:');
  console.log('- window.testOverlappingTextFix() - Test overlap fix');
  console.log('- window.testCurrentDataImproved() - Test with real data');
  console.log('- window.compareBeforeAfter() - Create comparison PDFs');
}