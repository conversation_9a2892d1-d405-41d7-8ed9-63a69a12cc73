/**
 * Test Type of Work Statistics Cards
 * Verifies that the statistics cards show correct counts
 */

/**
 * Test statistics cards functionality
 */
export const testStatisticsCards = () => {
  try {
    console.log('🧪 Testing Type of Work statistics cards...');
    console.log('');

    // Check if we're on the Type of Work page
    const currentPath = window.location.pathname;
    if (!currentPath.includes('type-of-work')) {
      console.log('⚠️ Please navigate to the Type of Work page first');
      return { success: false, message: 'Not on Type of Work page' };
    }

    // Look for statistics cards
    const statsCards = document.querySelectorAll('.grid .bg-white.rounded-lg.shadow-sm');
    console.log(`📊 Statistics cards found: ${statsCards.length}`);

    if (statsCards.length < 3) {
      console.log('❌ Expected 3 statistics cards, found:', statsCards.length);
      return { success: false, message: 'Statistics cards not found' };
    }

    // Extract statistics from cards
    const stats = {
      total: null,
      active: null,
      inactive: null
    };

    statsCards.forEach((card, index) => {
      const label = card.querySelector('.text-sm.font-medium')?.textContent?.trim();
      const value = card.querySelector('.text-2xl.font-bold')?.textContent?.trim();
      
      console.log(`Card ${index + 1}: ${label} = ${value}`);
      
      if (label?.includes('Total')) {
        stats.total = parseInt(value) || 0;
      } else if (label?.includes('Active')) {
        stats.active = parseInt(value) || 0;
      } else if (label?.includes('Inactive')) {
        stats.inactive = parseInt(value) || 0;
      }
    });

    console.log('');
    console.log('📊 Extracted Statistics:');
    console.log(`   Total Types: ${stats.total}`);
    console.log(`   Active Types: ${stats.active}`);
    console.log(`   Inactive Types: ${stats.inactive}`);

    // Verify math
    const calculatedTotal = stats.active + stats.inactive;
    const mathCorrect = stats.total === calculatedTotal;

    console.log('');
    console.log('🔢 Math Verification:');
    console.log(`   Active + Inactive = ${stats.active} + ${stats.inactive} = ${calculatedTotal}`);
    console.log(`   Total from card = ${stats.total}`);
    console.log(`   Math correct: ${mathCorrect ? '✅' : '❌'}`);

    // Count actual table rows for verification
    const tableRows = document.querySelectorAll('tbody tr');
    const actualTotal = tableRows.length;

    console.log('');
    console.log('📋 Table Verification:');
    console.log(`   Actual table rows: ${actualTotal}`);
    console.log(`   Card shows total: ${stats.total}`);
    console.log(`   Table matches card: ${actualTotal === stats.total ? '✅' : '❌'}`);

    // Count active/inactive from table
    let actualActive = 0;
    let actualInactive = 0;

    tableRows.forEach(row => {
      const statusCell = row.querySelector('td:nth-child(3)'); // Assuming status is 3rd column
      if (statusCell) {
        const statusText = statusCell.textContent.trim();
        if (statusText.includes('Active')) {
          actualActive++;
        } else if (statusText.includes('Inactive')) {
          actualInactive++;
        }
      }
    });

    console.log('');
    console.log('📊 Status Count Verification:');
    console.log(`   Table Active: ${actualActive}, Card Active: ${stats.active} ${actualActive === stats.active ? '✅' : '❌'}`);
    console.log(`   Table Inactive: ${actualInactive}, Card Inactive: ${stats.inactive} ${actualInactive === stats.inactive ? '✅' : '❌'}`);

    const allCorrect = mathCorrect && 
                      (actualTotal === stats.total) && 
                      (actualActive === stats.active) && 
                      (actualInactive === stats.inactive);

    console.log('');
    console.log(`🎯 Overall Result: ${allCorrect ? '✅ ALL CORRECT' : '❌ ISSUES FOUND'}`);

    return {
      success: true,
      stats: stats,
      verification: {
        mathCorrect: mathCorrect,
        totalMatches: actualTotal === stats.total,
        activeMatches: actualActive === stats.active,
        inactiveMatches: actualInactive === stats.inactive,
        allCorrect: allCorrect
      },
      actualCounts: {
        total: actualTotal,
        active: actualActive,
        inactive: actualInactive
      }
    };

  } catch (error) {
    console.error('💥 Statistics cards test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test card styling and layout
 */
export const testCardStyling = () => {
  try {
    console.log('🎨 Testing statistics card styling...');
    console.log('');

    const statsCards = document.querySelectorAll('.grid .bg-white.rounded-lg.shadow-sm');
    
    if (statsCards.length === 0) {
      console.log('❌ No statistics cards found');
      return { success: false, message: 'No cards found' };
    }

    const stylingResults = {
      cardCount: statsCards.length,
      hasIcons: 0,
      hasLabels: 0,
      hasValues: 0,
      hasColors: 0
    };

    statsCards.forEach((card, index) => {
      console.log(`🔍 Checking card ${index + 1}...`);
      
      // Check for icon
      const icon = card.querySelector('svg');
      if (icon) {
        stylingResults.hasIcons++;
        console.log(`   ✅ Has icon`);
      } else {
        console.log(`   ❌ Missing icon`);
      }
      
      // Check for label
      const label = card.querySelector('.text-sm.font-medium');
      if (label) {
        stylingResults.hasLabels++;
        console.log(`   ✅ Has label: "${label.textContent}"`);
      } else {
        console.log(`   ❌ Missing label`);
      }
      
      // Check for value
      const value = card.querySelector('.text-2xl.font-bold');
      if (value) {
        stylingResults.hasValues++;
        console.log(`   ✅ Has value: "${value.textContent}"`);
      } else {
        console.log(`   ❌ Missing value`);
      }
      
      // Check for color coding
      const coloredElement = card.querySelector('.text-green-600, .text-red-600, .text-blue-600');
      if (coloredElement) {
        stylingResults.hasColors++;
        console.log(`   ✅ Has color coding`);
      } else {
        console.log(`   ⚠️ No color coding found`);
      }
    });

    console.log('');
    console.log('📊 Styling Summary:');
    console.log(`   Cards found: ${stylingResults.cardCount}/3`);
    console.log(`   Cards with icons: ${stylingResults.hasIcons}/${stylingResults.cardCount}`);
    console.log(`   Cards with labels: ${stylingResults.hasLabels}/${stylingResults.cardCount}`);
    console.log(`   Cards with values: ${stylingResults.hasValues}/${stylingResults.cardCount}`);
    console.log(`   Cards with colors: ${stylingResults.hasColors}/${stylingResults.cardCount}`);

    const allStylingCorrect = stylingResults.cardCount === 3 &&
                             stylingResults.hasIcons === 3 &&
                             stylingResults.hasLabels === 3 &&
                             stylingResults.hasValues === 3;

    console.log('');
    console.log(`🎨 Styling Result: ${allStylingCorrect ? '✅ PERFECT' : '⚠️ NEEDS IMPROVEMENT'}`);

    return {
      success: true,
      styling: stylingResults,
      allStylingCorrect: allStylingCorrect
    };

  } catch (error) {
    console.error('💥 Card styling test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test responsive layout
 */
export const testResponsiveLayout = () => {
  try {
    console.log('📱 Testing responsive layout...');
    console.log('');

    const gridContainer = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-3');
    
    if (!gridContainer) {
      console.log('❌ Grid container not found');
      return { success: false, message: 'Grid container missing' };
    }

    console.log('✅ Grid container found');
    
    // Check grid classes
    const hasResponsiveClasses = gridContainer.classList.contains('grid-cols-1') &&
                                gridContainer.classList.contains('md:grid-cols-3');
    
    console.log(`📐 Responsive classes: ${hasResponsiveClasses ? '✅' : '❌'}`);
    console.log(`   - grid-cols-1: ${gridContainer.classList.contains('grid-cols-1') ? '✅' : '❌'}`);
    console.log(`   - md:grid-cols-3: ${gridContainer.classList.contains('md:grid-cols-3') ? '✅' : '❌'}`);
    
    // Check gap
    const hasGap = gridContainer.classList.contains('gap-6');
    console.log(`📏 Gap spacing: ${hasGap ? '✅' : '❌'}`);

    return {
      success: true,
      responsive: {
        gridFound: true,
        responsiveClasses: hasResponsiveClasses,
        hasGap: hasGap
      }
    };

  } catch (error) {
    console.error('💥 Responsive layout test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Run all statistics tests
 */
export const runAllStatsTests = () => {
  console.log('🚀 RUNNING ALL TYPE OF WORK STATISTICS TESTS...');
  console.log('');

  // Test 1: Statistics functionality
  const statsTest = testStatisticsCards();
  console.log('');

  // Test 2: Card styling
  const stylingTest = testCardStyling();
  console.log('');

  // Test 3: Responsive layout
  const responsiveTest = testResponsiveLayout();
  console.log('');

  // Summary
  console.log('📊 FINAL TEST SUMMARY:');
  console.log('');
  console.log('1️⃣ Statistics Functionality:');
  console.log(`   Status: ${statsTest.success ? '✅ PASSED' : '❌ FAILED'}`);
  if (statsTest.verification) {
    console.log(`   - Math correct: ${statsTest.verification.mathCorrect ? '✅' : '❌'}`);
    console.log(`   - Total matches: ${statsTest.verification.totalMatches ? '✅' : '❌'}`);
    console.log(`   - Active matches: ${statsTest.verification.activeMatches ? '✅' : '❌'}`);
    console.log(`   - Inactive matches: ${statsTest.verification.inactiveMatches ? '✅' : '❌'}`);
  }

  console.log('');
  console.log('2️⃣ Card Styling:');
  console.log(`   Status: ${stylingTest.success ? '✅ PASSED' : '❌ FAILED'}`);
  if (stylingTest.styling) {
    console.log(`   - Cards found: ${stylingTest.styling.cardCount}/3`);
    console.log(`   - All styled: ${stylingTest.allStylingCorrect ? '✅' : '❌'}`);
  }

  console.log('');
  console.log('3️⃣ Responsive Layout:');
  console.log(`   Status: ${responsiveTest.success ? '✅ PASSED' : '❌ FAILED'}`);
  if (responsiveTest.responsive) {
    console.log(`   - Grid found: ${responsiveTest.responsive.gridFound ? '✅' : '❌'}`);
    console.log(`   - Responsive: ${responsiveTest.responsive.responsiveClasses ? '✅' : '❌'}`);
  }

  console.log('');
  console.log('🎉 ALL STATISTICS TESTS COMPLETED!');

  return {
    statistics: statsTest,
    styling: stylingTest,
    responsive: responsiveTest
  };
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.testStatisticsCards = testStatisticsCards;
  window.testCardStyling = testCardStyling;
  window.testResponsiveLayout = testResponsiveLayout;
  window.runAllStatsTests = runAllStatsTests;
  
  console.log('📊 Type of Work Statistics Test Functions Available:');
  console.log('- window.testStatisticsCards() - Test statistics accuracy');
  console.log('- window.testCardStyling() - Test card appearance');
  console.log('- window.testResponsiveLayout() - Test responsive design');
  console.log('- window.runAllStatsTests() - Run all tests');
}
