import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { isAuthenticated, clearAllAuthData } from '../../services/enhancedAuthService';

const RouteGuard = ({ children }) => {
  const location = useLocation();

  useEffect(() => {
    // Define public routes that don't require authentication
    const publicRoutes = ['/login'];
    const testRoutes = ['/test-react', '/test-s3', '/test-upload'];
    const isPublicRoute = publicRoutes.includes(location.pathname) || 
                         testRoutes.some(route => location.pathname.startsWith(route));

    console.log('🛡️ RouteGuard: Checking route:', location.pathname);
    console.log('🛡️ RouteGuard: Is public route:', isPublicRoute);

    // If trying to access a protected route
    if (!isPublicRoute) {
      const authenticated = isAuthenticated();
      console.log('🛡️ RouteGuard: Authentication status:', authenticated);

      if (!authenticated) {
        console.log('🛡️ RouteGuard: BLOCKING access to protected route');
        console.log('🛡️ RouteGuard: Clearing all auth data and redirecting');
        
        // Clear all authentication data
        clearAllAuthData();
        
        // Prevent any further execution
        window.location.replace('/login');
        return;
      }
    }

    // If on login page but already authenticated, redirect to dashboard
    if (location.pathname === '/login' && isAuthenticated()) {
      console.log('🛡️ RouteGuard: Already authenticated, redirecting to dashboard');
      window.location.replace('/dashboard');
      return;
    }

    // Global protection against direct URL manipulation
    const handleLocationChange = () => {
      const currentPath = window.location.pathname;
      const isCurrentPublic = publicRoutes.includes(currentPath) || 
                             testRoutes.some(route => currentPath.startsWith(route));

      if (!isCurrentPublic && !isAuthenticated()) {
        console.log('🛡️ RouteGuard: Direct URL access blocked');
        clearAllAuthData();
        window.location.replace('/login');
      }
    };

    // Monitor for URL changes (including direct typing in address bar)
    const observer = new MutationObserver(handleLocationChange);
    observer.observe(document, { subtree: true, childList: true });

    // Also check on focus (when user returns to tab)
    const handleFocus = () => {
      handleLocationChange();
    };

    window.addEventListener('focus', handleFocus);

    // Cleanup
    return () => {
      observer.disconnect();
      window.removeEventListener('focus', handleFocus);
    };
  }, [location.pathname]);

  return children;
};

export default RouteGuard;
