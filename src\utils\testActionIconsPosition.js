/**
 * Test Action Icons Position and Description Tooltips
 * Verifies the action icons are in the header and description tooltips work
 */

/**
 * Test action icons position in header
 */
export const testActionIconsPosition = () => {
  try {
    console.log('🔧 Testing action icons position in header...');
    console.log('');

    // Check if we're on the Services page
    const currentPath = window.location.pathname;
    if (!currentPath.includes('services')) {
      console.log('⚠️ Please navigate to the Services page first');
      return { success: false, message: 'Not on Services page' };
    }

    // Look for the DataTable header area
    const tableHeader = document.querySelector('.card h2, .card .text-lg, .card .font-semibold');
    
    if (!tableHeader) {
      console.log('❌ Table header not found');
      return { success: false, message: 'Table header not found' };
    }

    console.log('✅ Table header found');

    // Look for action icons in the header area (should be near the title)
    const headerContainer = tableHeader.closest('.card') || tableHeader.closest('div');
    const actionIconsInHeader = headerContainer?.querySelector('[class*="Action Icons"]') || 
                               headerContainer?.textContent?.includes('Action Icons:');

    console.log(`📍 Action icons in header area: ${actionIconsInHeader ? '✅ FOUND' : '❌ NOT FOUND'}`);

    // Look for action icons legend elements
    let actionIconsFound = false;
    let duplicateActionIconsFound = false;

    // Search for action icons text
    const allSpans = document.querySelectorAll('span');
    let actionIconsCount = 0;

    allSpans.forEach(span => {
      if (span.textContent.includes('Action Icons:')) {
        actionIconsCount++;
        actionIconsFound = true;
        console.log(`✅ Action Icons legend text found (${actionIconsCount})`);

        // Check if it's in the right position (near table header)
        const isNearHeader = span.closest('.card') !== null;
        console.log(`📍 Position near table: ${isNearHeader ? '✅ CORRECT' : '❌ WRONG'}`);
      }
    });

    if (!actionIconsFound) {
      console.log('❌ Action Icons legend not found');
    } else if (actionIconsCount > 1) {
      duplicateActionIconsFound = true;
      console.log(`⚠️ DUPLICATE Action Icons found: ${actionIconsCount} instances`);
    } else {
      console.log('✅ Single Action Icons legend found (correct)');
    }

    // Check for individual action icon elements
    const viewIcons = document.querySelectorAll('svg[class*="h-4"][class*="w-4"]');
    console.log(`🔧 Action icon elements found: ${viewIcons.length}`);

    return {
      success: true,
      headerFound: !!tableHeader,
      actionIconsInHeader: !!actionIconsInHeader,
      actionIconsFound: actionIconsFound,
      duplicateActionIconsFound: duplicateActionIconsFound,
      iconElementsCount: viewIcons.length
    };

  } catch (error) {
    console.error('💥 Action icons position test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test description column tooltips (native HTML title attribute)
 */
export const testDescriptionTooltips = () => {
  try {
    console.log('💬 Testing description column tooltips (native HTML title)...');
    console.log('');

    // Look for description cells with title attribute
    const descriptionCells = document.querySelectorAll('td');
    let descriptionCellsFound = 0;
    let titleTooltipCellsFound = 0;
    let truncatedCellsFound = 0;

    descriptionCells.forEach((cell, index) => {
      const text = cell.textContent.trim();

      // Check if this looks like a description cell
      if (text.length > 10 && !text.includes('Active') && !text.includes('Inactive') &&
          !text.includes('/') && !text.match(/^\d+$/)) {

        descriptionCellsFound++;
        console.log(`📝 Description cell ${descriptionCellsFound}: "${text.substring(0, 30)}${text.length > 30 ? '...' : ''}"`);

        // Check if cell has truncate class
        const truncateDiv = cell.querySelector('.truncate');
        if (truncateDiv) {
          truncatedCellsFound++;
          console.log(`   ✂️ Has truncate styling`);

          // Check if it has title attribute (native tooltip)
          const hasTitle = truncateDiv.hasAttribute('title');
          if (hasTitle) {
            titleTooltipCellsFound++;
            const titleValue = truncateDiv.getAttribute('title');
            console.log(`   💬 Has native title tooltip: "${titleValue.substring(0, 30)}${titleValue.length > 30 ? '...' : ''}"`);
          }
        }
      }
    });

    console.log('');
    console.log('💬 Native Tooltip Analysis:');
    console.log(`   Description cells found: ${descriptionCellsFound}`);
    console.log(`   Cells with truncate: ${truncatedCellsFound}`);
    console.log(`   Cells with title attribute: ${titleTooltipCellsFound}`);

    const tooltipsWorking = titleTooltipCellsFound > 0 && truncatedCellsFound > 0;

    console.log('');
    console.log(`💬 Native Tooltips Result: ${tooltipsWorking ? '✅ WORKING (like Type of Work)' : '❌ NOT WORKING'}`);

    return {
      success: true,
      descriptionCells: descriptionCellsFound,
      truncatedCells: truncatedCellsFound,
      titleTooltipCells: titleTooltipCellsFound,
      tooltipsWorking: tooltipsWorking
    };

  } catch (error) {
    console.error('💥 Description tooltips test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test description column sorting removal
 */
export const testDescriptionSortingRemoval = () => {
  try {
    console.log('🚫 Testing description column sorting removal...');
    console.log('');

    // Look for table headers
    const headers = document.querySelectorAll('th');
    let descriptionHeader = null;
    
    headers.forEach((header, index) => {
      const text = header.textContent.trim();
      console.log(`Header ${index + 1}: "${text}"`);
      
      if (text.includes('Description')) {
        descriptionHeader = header;
      }
    });

    if (!descriptionHeader) {
      console.log('❌ Description header not found');
      return { success: false, message: 'Description header not found' };
    }

    console.log('✅ Description header found');

    // Check if description header has sorting functionality
    const hasSortingButton = descriptionHeader.querySelector('button');
    const hasSortingIcon = descriptionHeader.querySelector('svg');
    const isClickable = descriptionHeader.style.cursor === 'pointer' || 
                       descriptionHeader.classList.contains('cursor-pointer');

    console.log('');
    console.log('🚫 Sorting Analysis:');
    console.log(`   Has sorting button: ${hasSortingButton ? '❌ YES (should be removed)' : '✅ NO'}`);
    console.log(`   Has sorting icon: ${hasSortingIcon ? '❌ YES (should be removed)' : '✅ NO'}`);
    console.log(`   Is clickable: ${isClickable ? '❌ YES (should be removed)' : '✅ NO'}`);

    const sortingRemoved = !hasSortingButton && !hasSortingIcon && !isClickable;

    console.log('');
    console.log(`🚫 Sorting Removal Result: ${sortingRemoved ? '✅ REMOVED' : '❌ STILL PRESENT'}`);

    return {
      success: true,
      descriptionHeaderFound: !!descriptionHeader,
      hasSortingButton: !!hasSortingButton,
      hasSortingIcon: !!hasSortingIcon,
      isClickable: isClickable,
      sortingRemoved: sortingRemoved
    };

  } catch (error) {
    console.error('💥 Description sorting removal test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test native HTML title tooltip functionality
 */
export const testDescriptionHover = () => {
  try {
    console.log('👆 Testing native HTML title tooltip functionality...');
    console.log('');

    // Look for description cells with title attribute
    const titleElements = document.querySelectorAll('[title]');
    let descriptionTitleElements = 0;
    let validTitleElements = 0;

    console.log(`👆 Elements with title attribute found: ${titleElements.length}`);

    titleElements.forEach((element, index) => {
      const titleValue = element.getAttribute('title');
      const elementText = element.textContent.trim();

      // Check if this looks like a description element
      if (titleValue && titleValue.length > 10 && elementText.length > 10 &&
          !titleValue.includes('Active') && !titleValue.includes('Inactive')) {

        descriptionTitleElements++;
        console.log(`💬 Description title ${descriptionTitleElements}: "${titleValue.substring(0, 40)}${titleValue.length > 40 ? '...' : ''}"`);

        // Check if element has truncate class (proper implementation)
        const hasTruncate = element.classList.contains('truncate');
        if (hasTruncate) {
          validTitleElements++;
          console.log(`   ✅ Has truncate class (proper implementation)`);
        }

        // Check if title matches or contains the visible text
        const titleMatchesText = titleValue === elementText || titleValue.includes(elementText);
        if (titleMatchesText) {
          console.log(`   ✅ Title matches visible text`);
        } else {
          console.log(`   ⚠️ Title doesn't match visible text`);
        }
      }
    });

    console.log('');
    console.log('👆 Native Title Tooltip Analysis:');
    console.log(`   Total title elements: ${titleElements.length}`);
    console.log(`   Description title elements: ${descriptionTitleElements}`);
    console.log(`   Valid truncated elements: ${validTitleElements}`);

    const hoverWorking = validTitleElements > 0;

    console.log('');
    console.log(`👆 Native Tooltip Result: ${hoverWorking ? '✅ WORKING (like Type of Work)' : '❌ NOT WORKING'}`);
    console.log('');
    console.log('📋 How to test:');
    console.log('   1. Find a description cell with truncated text (...)');
    console.log('   2. Hover over the text');
    console.log('   3. Browser should show native tooltip with full text');

    return {
      success: true,
      titleElements: titleElements.length,
      descriptionTitleElements: descriptionTitleElements,
      validTitleElements: validTitleElements,
      hoverWorking: hoverWorking
    };

  } catch (error) {
    console.error('💥 Description hover test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Run all action icons and description tests
 */
export const runActionIconsAndDescriptionTests = () => {
  console.log('🚀 RUNNING ACTION ICONS POSITION & DESCRIPTION TESTS...');
  console.log('');

  const results = {};

  // Test 1: Action icons position
  console.log('1️⃣ Testing Action Icons Position...');
  results.position = testActionIconsPosition();
  console.log('');

  // Test 2: Description tooltips
  console.log('2️⃣ Testing Description Tooltips...');
  results.tooltips = testDescriptionTooltips();
  console.log('');

  // Test 3: Description sorting removal
  console.log('3️⃣ Testing Description Sorting Removal...');
  results.sorting = testDescriptionSortingRemoval();
  console.log('');

  // Test 4: Description hover
  console.log('4️⃣ Testing Description Hover...');
  results.hover = testDescriptionHover();
  console.log('');

  // Summary
  setTimeout(() => {
    console.log('📋 ACTION ICONS & DESCRIPTION TEST SUMMARY:');
    console.log('');
    console.log('1️⃣ Action Icons Position:');
    console.log(`   Status: ${results.position.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (results.position.actionIconsInHeader !== undefined) {
      console.log(`   - In header area: ${results.position.actionIconsInHeader ? '✅' : '❌'}`);
    }
    if (results.position.duplicateActionIconsFound !== undefined) {
      console.log(`   - No duplicates: ${!results.position.duplicateActionIconsFound ? '✅' : '❌'}`);
    }

    console.log('');
    console.log('2️⃣ Description Tooltips (Native HTML):');
    console.log(`   Status: ${results.tooltips.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (results.tooltips.tooltipsWorking !== undefined) {
      console.log(`   - Native tooltips working: ${results.tooltips.tooltipsWorking ? '✅' : '❌'}`);
    }

    console.log('');
    console.log('3️⃣ Description Sorting:');
    console.log(`   Status: ${results.sorting.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (results.sorting.sortingRemoved !== undefined) {
      console.log(`   - Sorting removed: ${results.sorting.sortingRemoved ? '✅' : '❌'}`);
    }

    console.log('');
    console.log('4️⃣ Native Title Tooltips:');
    console.log(`   Status: ${results.hover.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (results.hover.hoverWorking !== undefined) {
      console.log(`   - Native tooltips working: ${results.hover.hoverWorking ? '✅' : '❌'}`);
    }

    console.log('');
    console.log('🎉 ALL TESTS COMPLETED!');
    console.log('');
    console.log('📋 Manual Testing Instructions:');
    console.log('1. Check that Action Icons are between title and Columns/Export buttons');
    console.log('2. Hover over truncated descriptions to see native browser tooltip');
    console.log('3. Verify Description column header is not clickable/sortable');
    console.log('4. Test on both Services and Categories tabs');
    console.log('5. Compare with Type of Work page - should work identically');
  }, 1000);

  return results;
};

/**
 * Compare with Type of Work page implementation
 */
export const compareWithTypeOfWork = () => {
  try {
    console.log('🔄 Comparing with Type of Work page implementation...');
    console.log('');

    const currentPath = window.location.pathname;
    console.log(`📍 Current page: ${currentPath}`);

    if (currentPath.includes('type-of-work')) {
      console.log('✅ On Type of Work page - this is the reference implementation');
      console.log('');
      console.log('📋 Reference Implementation Analysis:');

      // Analyze Type of Work page
      const descriptionCells = document.querySelectorAll('td .truncate[title]');
      console.log(`   Description cells with title: ${descriptionCells.length}`);

      descriptionCells.forEach((cell, index) => {
        if (index < 3) { // Show first 3 examples
          const title = cell.getAttribute('title');
          const text = cell.textContent.trim();
          console.log(`   Example ${index + 1}: "${text}" → title: "${title.substring(0, 30)}..."`);
        }
      });

      return { success: true, isReference: true, descriptionCells: descriptionCells.length };

    } else if (currentPath.includes('services')) {
      console.log('🔧 On Services page - comparing with Type of Work implementation');
      console.log('');

      // Check if implementation matches Type of Work
      const descriptionCells = document.querySelectorAll('td .truncate[title]');
      const hasCorrectImplementation = descriptionCells.length > 0;

      console.log(`📊 Implementation Analysis:`);
      console.log(`   Description cells with title attribute: ${descriptionCells.length}`);
      console.log(`   Matches Type of Work pattern: ${hasCorrectImplementation ? '✅ YES' : '❌ NO'}`);

      if (hasCorrectImplementation) {
        console.log('');
        console.log('✅ PERFECT MATCH! Implementation identical to Type of Work page');
        console.log('   - Uses native HTML title attribute');
        console.log('   - Has truncate class for text overflow');
        console.log('   - Browser handles tooltip automatically');
      } else {
        console.log('');
        console.log('❌ MISMATCH! Implementation differs from Type of Work page');
      }

      return {
        success: true,
        isReference: false,
        descriptionCells: descriptionCells.length,
        matchesReference: hasCorrectImplementation
      };

    } else {
      console.log('⚠️ Please navigate to Services or Type of Work page for comparison');
      return { success: false, message: 'Wrong page for comparison' };
    }

  } catch (error) {
    console.error('💥 Comparison test failed:', error);
    return { success: false, error: error.message };
  }
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.testActionIconsPosition = testActionIconsPosition;
  window.testDescriptionTooltips = testDescriptionTooltips;
  window.testDescriptionSortingRemoval = testDescriptionSortingRemoval;
  window.testDescriptionHover = testDescriptionHover;
  window.runActionIconsAndDescriptionTests = runActionIconsAndDescriptionTests;
  window.compareWithTypeOfWork = compareWithTypeOfWork;

  console.log('🔧 Action Icons & Description Test Functions Available:');
  console.log('- window.testActionIconsPosition() - Test action icons position');
  console.log('- window.testDescriptionTooltips() - Test native HTML tooltips');
  console.log('- window.testDescriptionSortingRemoval() - Test sorting removal');
  console.log('- window.testDescriptionHover() - Test native title tooltips');
  console.log('- window.runActionIconsAndDescriptionTests() - Run all tests');
  console.log('- window.compareWithTypeOfWork() - Compare with Type of Work page');
}
