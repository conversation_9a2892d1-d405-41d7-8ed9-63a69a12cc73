/**
 * Area of Expertise Service
 * Handles CRUD operations for area of expertise data
 */

import { sql } from '../config/database.js';

/**
 * Get all area of expertise entries
 */
export const getAllAreaOfExpertise = async () => {
  try {
    console.log('📚 Fetching all area of expertise...');
    
    const result = await sql`
      SELECT 
        id,
        name,
        description,
        is_active,
        created_at,
        updated_at
      FROM area_of_expertise 
      ORDER BY name ASC
    `;

    const areaOfExpertise = result.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description || '',
      isActive: row.is_active,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }));

    console.log(`✅ Retrieved ${areaOfExpertise.length} area of expertise entries`);
    return areaOfExpertise;

  } catch (error) {
    console.error('❌ Error fetching area of expertise:', error);
    throw error;
  }
};

/**
 * Get only active area of expertise entries
 */
export const getActiveAreaOfExpertise = async () => {
  try {
    console.log('📚 Fetching active area of expertise...');
    
    const result = await sql`
      SELECT 
        id,
        name,
        description,
        is_active,
        created_at,
        updated_at
      FROM area_of_expertise 
      WHERE is_active = true
      ORDER BY name ASC
    `;

    const areaOfExpertise = result.map(row => {
      // Sanitize description to prevent UI issues but keep all valid data
      let cleanDescription = row.description || '';

      // Only clean if there's obvious repetition (same word repeated 3+ times)
      const words = cleanDescription.split(' ');
      if (words.length > 5) {
        // Check for excessive repetition of the same word
        const wordCount = {};
        words.forEach(word => {
          wordCount[word] = (wordCount[word] || 0) + 1;
        });

        // If any word appears more than 3 times, it's likely corrupted
        const hasRepetition = Object.values(wordCount).some(count => count > 3);

        if (hasRepetition) {
          // Remove excessive repetition but keep meaningful content
          const uniqueWords = [];
          let consecutiveCount = 1;
          let lastWord = '';

          for (const word of words) {
            if (word === lastWord) {
              consecutiveCount++;
              // Only add if it's not the 3rd+ consecutive repetition
              if (consecutiveCount <= 2) {
                uniqueWords.push(word);
              }
            } else {
              uniqueWords.push(word);
              consecutiveCount = 1;
            }
            lastWord = word;

            // Stop if description gets too long (500 chars)
            if (uniqueWords.join(' ').length > 500) break;
          }
          cleanDescription = uniqueWords.join(' ');
        }
      }

      // Only truncate if extremely long (over 500 characters)
      if (cleanDescription.length > 500) {
        cleanDescription = cleanDescription.substring(0, 500) + '...';
      }

      return {
        id: row.id,
        name: row.name,
        description: cleanDescription,
        isActive: row.is_active,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      };
    });

    console.log(`✅ Retrieved ${areaOfExpertise.length} active area of expertise entries`);
    console.log('📋 Area of Expertise data:', areaOfExpertise.map(item => ({ id: item.id, name: item.name })));
    return areaOfExpertise;

  } catch (error) {
    console.error('❌ Error fetching active area of expertise:', error);
    throw error;
  }
};

/**
 * Get area of expertise by ID
 */
export const getAreaOfExpertiseById = async (id) => {
  try {
    console.log(`📚 Fetching area of expertise with ID: ${id}`);
    
    const result = await sql`
      SELECT 
        id,
        name,
        description,
        is_active,
        created_at,
        updated_at
      FROM area_of_expertise 
      WHERE id = ${id}
    `;

    if (result.length === 0) {
      throw new Error(`Area of expertise with ID ${id} not found`);
    }

    const row = result[0];
    const areaOfExpertise = {
      id: row.id,
      name: row.name,
      description: row.description || '',
      isActive: row.is_active,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };

    console.log(`✅ Retrieved area of expertise: ${areaOfExpertise.name}`);
    return areaOfExpertise;

  } catch (error) {
    console.error(`❌ Error fetching area of expertise ${id}:`, error);
    throw error;
  }
};

/**
 * Create new area of expertise
 */
export const createAreaOfExpertise = async (areaOfExpertiseData) => {
  try {
    const { name, description = '', isActive = true } = areaOfExpertiseData;

    console.log('📚 Creating new area of expertise...');
    console.log('  - Name:', name);
    console.log('  - Description:', description);
    console.log('  - Is Active:', isActive);

    // Check if area of expertise with same name already exists
    const existingCheck = await sql`
      SELECT id FROM area_of_expertise 
      WHERE LOWER(name) = LOWER(${name})
    `;

    if (existingCheck.length > 0) {
      throw new Error(`Area of expertise with name "${name}" already exists`);
    }

    const result = await sql`
      INSERT INTO area_of_expertise (name, description, is_active)
      VALUES (${name}, ${description}, ${isActive})
      RETURNING id, name, description, is_active, created_at, updated_at
    `;

    const newAreaOfExpertise = {
      id: result[0].id,
      name: result[0].name,
      description: result[0].description || '',
      isActive: result[0].is_active,
      createdAt: result[0].created_at,
      updatedAt: result[0].updated_at
    };

    console.log(`✅ Area of expertise created successfully with ID: ${newAreaOfExpertise.id}`);
    return newAreaOfExpertise;

  } catch (error) {
    console.error('❌ Error creating area of expertise:', error);
    throw error;
  }
};

/**
 * Update area of expertise
 */
export const updateAreaOfExpertise = async (id, areaOfExpertiseData) => {
  try {
    console.log(`📚 Updating area of expertise with ID: ${id}`);
    
    const { name, description, isActive } = areaOfExpertiseData;

    // Check if area of expertise exists
    const existingAreaOfExpertise = await getAreaOfExpertiseById(id);
    if (!existingAreaOfExpertise) {
      throw new Error(`Area of expertise with ID ${id} not found`);
    }

    // Check if another area of expertise with same name exists (excluding current one)
    if (name && name !== existingAreaOfExpertise.name) {
      const duplicateCheck = await sql`
        SELECT id FROM area_of_expertise 
        WHERE LOWER(name) = LOWER(${name}) AND id != ${id}
      `;

      if (duplicateCheck.length > 0) {
        throw new Error(`Area of expertise with name "${name}" already exists`);
      }
    }

    const result = await sql`
      UPDATE area_of_expertise 
      SET 
        name = COALESCE(${name}, name),
        description = COALESCE(${description}, description),
        is_active = COALESCE(${isActive}, is_active),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING id, name, description, is_active, created_at, updated_at
    `;

    const updatedAreaOfExpertise = {
      id: result[0].id,
      name: result[0].name,
      description: result[0].description || '',
      isActive: result[0].is_active,
      createdAt: result[0].created_at,
      updatedAt: result[0].updated_at
    };

    console.log(`✅ Area of expertise updated successfully: ${updatedAreaOfExpertise.name}`);
    return updatedAreaOfExpertise;

  } catch (error) {
    console.error(`❌ Error updating area of expertise ${id}:`, error);
    throw error;
  }
};

/**
 * Delete area of expertise (soft delete by setting is_active to false)
 */
export const deleteAreaOfExpertise = async (id) => {
  try {
    console.log(`📚 Deleting area of expertise with ID: ${id}`);

    // Check if area of expertise exists
    const existingAreaOfExpertise = await getAreaOfExpertiseById(id);
    if (!existingAreaOfExpertise) {
      throw new Error(`Area of expertise with ID ${id} not found`);
    }

    await sql`
      UPDATE area_of_expertise 
      SET is_active = false, updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
    `;

    console.log(`✅ Area of expertise deleted successfully: ${existingAreaOfExpertise.name}`);
    return { success: true, message: 'Area of expertise deleted successfully' };

  } catch (error) {
    console.error(`❌ Error deleting area of expertise ${id}:`, error);
    throw error;
  }
};

/**
 * Restore area of expertise (set is_active to true)
 */
export const restoreAreaOfExpertise = async (id) => {
  try {
    console.log(`📚 Restoring area of expertise with ID: ${id}`);

    const result = await sql`
      UPDATE area_of_expertise 
      SET is_active = true, updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING name
    `;

    if (result.length === 0) {
      throw new Error(`Area of expertise with ID ${id} not found`);
    }

    console.log(`✅ Area of expertise restored successfully: ${result[0].name}`);
    return { success: true, message: 'Area of expertise restored successfully' };

  } catch (error) {
    console.error(`❌ Error restoring area of expertise ${id}:`, error);
    throw error;
  }
};

/**
 * Search area of expertise by name
 */
export const searchAreaOfExpertise = async (searchTerm) => {
  try {
    console.log(`📚 Searching area of expertise with term: ${searchTerm}`);
    
    const result = await sql`
      SELECT 
        id,
        name,
        description,
        is_active,
        created_at,
        updated_at
      FROM area_of_expertise 
      WHERE 
        (LOWER(name) LIKE LOWER(${'%' + searchTerm + '%'}) OR 
         LOWER(description) LIKE LOWER(${'%' + searchTerm + '%'}))
        AND is_active = true
      ORDER BY name ASC
    `;

    const areaOfExpertise = result.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description || '',
      isActive: row.is_active,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }));

    console.log(`✅ Found ${areaOfExpertise.length} area of expertise entries matching "${searchTerm}"`);
    return areaOfExpertise;

  } catch (error) {
    console.error('❌ Error searching area of expertise:', error);
    throw error;
  }
};
