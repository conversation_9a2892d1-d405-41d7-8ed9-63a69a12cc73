import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { isAuthenticated, clearAllAuthData } from '../../services/enhancedAuthService';

const AuthGuard = ({ children }) => {
  const location = useLocation();

  useEffect(() => {
    // Simple authentication guard
    const isPublicRoute = location.pathname === '/login' ||
                         location.pathname.startsWith('/test-');

    // Only check auth for protected routes
    if (!isPublicRoute && !isAuthenticated()) {
      console.log('🔐 AuthGuard: Not authenticated, redirecting to login');
      clearAllAuthData();
      window.location.replace('/login');
    }

    // Handle browser back button after logout
    const handlePopState = () => {
      const currentIsPublic = window.location.pathname === '/login' ||
                             window.location.pathname.startsWith('/test-');

      if (!currentIsPublic && !isAuthenticated()) {
        console.log('🔐 AuthGuard: Back button blocked, redirecting to login');
        clearAllAuthData();
        window.location.replace('/login');
      }
    };

    // Storage change guard (detects logout in other tabs)
    const handleStorageChange = (event) => {
      if (event.key === 'innoventory_auth' && !event.newValue) {
        console.log('🔐 AuthGuard: Logged out in another tab, redirecting');
        window.location.replace('/login');
      }
    };

    window.addEventListener('popstate', handlePopState);
    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('popstate', handlePopState);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [location.pathname]);

  return children;
};

export default AuthGuard;
