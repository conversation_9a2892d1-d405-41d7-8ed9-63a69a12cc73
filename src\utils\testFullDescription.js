/**
 * Test Full Description Export
 * Verifies that description text shows fully and actions column is excluded
 */

/**
 * Test full description text in PDF
 */
export const testFullDescriptionPDF = async () => {
  try {
    console.log('🧪 Testing full description text in PDF export...');
    
    // Dynamic import
    const jsPDFModule = await import('jspdf');
    await import('jspdf-autotable');
    
    const jsPDFClass = jsPDFModule.default;
    const doc = new jsPDFClass();
    
    // Add title
    doc.setFontSize(16);
    doc.text('Full Description Test', 14, 22);
    
    // Add timestamp
    doc.setFontSize(10);
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 14, 32);
    
    // Test data with very long descriptions
    const headers = ['Work Type', 'Description', 'Status', 'Created Date', 'Updated Date'];
    const rows = [
      [
        'Patent Filing',
        'This is a comprehensive patent filing service that includes detailed research, documentation preparation, prior art analysis, claim drafting, and submission to relevant patent offices worldwide.',
        'Active',
        '5/7/2025',
        '5/7/2025'
      ],
      [
        'Software Development',
        'Custom software development and programming services including full-stack web development, mobile app development, database design, API integration, testing, deployment, and ongoing maintenance support.',
        'Active',
        '16/7/2025',
        'N/A'
      ],
      [
        'Business Analysis',
        'Comprehensive business intelligence and data analytics services including market research, competitive analysis, financial modeling, performance metrics, dashboard creation, and strategic recommendations.',
        'Inactive',
        '21/7/2025',
        '21/7/2025'
      ]
    ];
    
    console.log('📋 Testing with long descriptions...');
    console.log('📄 Description lengths:', rows.map(row => row[1].length));
    
    if (typeof doc.autoTable === 'function') {
      // Calculate column widths with more space for description
      const pageWidth = doc.internal.pageSize.getWidth();
      const margins = 28;
      const availableWidth = pageWidth - margins;
      
      const columnRatios = {
        'Work Type': 0.12,
        'Description': 0.45,  // 45% for description
        'Status': 0.10,
        'Created Date': 0.165,
        'Updated Date': 0.165
      };
      
      const columnStyles = {};
      headers.forEach((header, index) => {
        const ratio = columnRatios[header] || (1 / headers.length);
        const width = availableWidth * ratio;
        columnStyles[index] = { cellWidth: width };
      });

      doc.autoTable({
        head: [headers],
        body: rows,
        startY: 40,
        styles: {
          fontSize: 8,
          cellPadding: 2,
          lineColor: [0, 0, 0],
          lineWidth: 0.1,
          textColor: [0, 0, 0],
          overflow: 'linebreak',
          cellWidth: 'wrap',
          valign: 'top'  // Align text to top of cell
        },
        headStyles: {
          fillColor: [59, 130, 246],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
          lineColor: [0, 0, 0],
          lineWidth: 0.2,
          fontSize: 9
        },
        bodyStyles: {
          lineColor: [0, 0, 0],
          lineWidth: 0.1,
          minCellHeight: 15  // Minimum cell height for wrapping
        },
        alternateRowStyles: {
          fillColor: [248, 250, 252],
        },
        columnStyles: columnStyles,
        tableLineColor: [0, 0, 0],
        tableLineWidth: 0.2,
        margin: { top: 40, left: 14, right: 14 },
        theme: 'grid'
      });
      
      console.log('✅ Full description PDF with text wrapping created!');
    } else {
      console.log('❌ autoTable not available');
      return false;
    }
    
    doc.save('full_description_test.pdf');
    console.log('✅ Full description test completed!');
    return true;
    
  } catch (error) {
    console.error('❌ Full description test failed:', error);
    return false;
  }
};

/**
 * Test actions column exclusion
 */
export const testActionsColumnExclusion = () => {
  try {
    console.log('🧪 Testing actions column exclusion...');
    
    // Mock columns data similar to Type of Work
    const mockColumns = [
      { key: 'name', label: 'Work Type' },
      { key: 'description', label: 'Description' },
      { key: 'status', label: 'Status' },
      { key: 'createdDate', label: 'Created Date' },
      { key: 'updatedDate', label: 'Updated Date' },
      { key: 'actions', label: 'Actions', filterable: false }
    ];
    
    // Test the filtering logic
    const filteredColumns = mockColumns.filter(col =>
      col.key !== 'actions' &&
      !col.key.includes('action')
    );
    
    console.log('📋 Original columns:', mockColumns.map(col => col.label));
    console.log('📋 Filtered columns:', filteredColumns.map(col => col.label));
    
    const hasActions = filteredColumns.some(col => col.key === 'actions');
    
    if (hasActions) {
      console.log('❌ Actions column is still included!');
      return false;
    } else {
      console.log('✅ Actions column successfully excluded!');
      return true;
    }
    
  } catch (error) {
    console.error('❌ Actions column exclusion test failed:', error);
    return false;
  }
};

/**
 * Test current page export with improvements
 */
export const testCurrentPageFullExport = async () => {
  try {
    console.log('🧪 Testing current page export with full descriptions...');
    
    // Get current page data
    const tables = document.querySelectorAll('table');
    if (tables.length === 0) {
      console.log('❌ No tables found on page');
      return false;
    }
    
    const table = tables[0];
    const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
    const rows = Array.from(table.querySelectorAll('tbody tr')).map(tr => 
      Array.from(tr.querySelectorAll('td')).map(td => {
        let text = td.textContent.trim();
        // Clean up text but don't truncate
        text = text.replace(/\s+/g, ' ');
        return text;
      })
    );
    
    console.log('📋 Headers found:', headers);
    console.log('📄 Rows found:', rows.length);
    
    // Check if Actions column is present
    const hasActionsColumn = headers.some(header => 
      header.toLowerCase().includes('action')
    );
    
    console.log(`🔧 Actions column present: ${hasActionsColumn}`);
    
    // Filter out actions column if present
    let filteredHeaders = headers;
    let filteredRows = rows;
    
    if (hasActionsColumn) {
      const actionsIndex = headers.findIndex(header => 
        header.toLowerCase().includes('action')
      );
      
      if (actionsIndex !== -1) {
        filteredHeaders = headers.filter((_, index) => index !== actionsIndex);
        filteredRows = rows.map(row => 
          row.filter((_, index) => index !== actionsIndex)
        );
        
        console.log('✅ Actions column filtered out for export');
      }
    }
    
    // Check description lengths
    const descriptionIndex = filteredHeaders.findIndex(header => 
      header.toLowerCase().includes('description')
    );
    
    if (descriptionIndex !== -1) {
      const descriptionLengths = filteredRows.map(row => 
        row[descriptionIndex] ? row[descriptionIndex].length : 0
      );
      
      console.log('📝 Description lengths:', descriptionLengths);
      console.log('📝 Longest description:', Math.max(...descriptionLengths), 'characters');
    }
    
    // Create PDF with full descriptions
    const jsPDFModule = await import('jspdf');
    await import('jspdf-autotable');
    
    const jsPDFClass = jsPDFModule.default;
    const doc = new jsPDFClass();
    
    // Add title
    doc.setFontSize(16);
    doc.text('Type of Work - Full Export', 14, 22);
    
    // Add timestamp
    doc.setFontSize(10);
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 14, 32);
    
    if (typeof doc.autoTable === 'function') {
      // Calculate column widths
      const pageWidth = doc.internal.pageSize.getWidth();
      const margins = 28;
      const availableWidth = pageWidth - margins;
      
      const columnRatios = {
        'Work Type': 0.12,
        'Description': 0.45,
        'Status': 0.10,
        'Created Date': 0.165,
        'Updated Date': 0.165
      };
      
      const columnStyles = {};
      filteredHeaders.forEach((header, index) => {
        const ratio = columnRatios[header] || (1 / filteredHeaders.length);
        const width = availableWidth * ratio;
        columnStyles[index] = { cellWidth: width };
      });

      doc.autoTable({
        head: [filteredHeaders],
        body: filteredRows,
        startY: 40,
        styles: {
          fontSize: 8,
          cellPadding: 2,
          lineColor: [0, 0, 0],
          lineWidth: 0.1,
          textColor: [0, 0, 0],
          overflow: 'linebreak',
          cellWidth: 'wrap',
          valign: 'top'
        },
        headStyles: {
          fillColor: [59, 130, 246],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
          lineColor: [0, 0, 0],
          lineWidth: 0.2,
          fontSize: 9
        },
        bodyStyles: {
          lineColor: [0, 0, 0],
          lineWidth: 0.1,
          minCellHeight: 15
        },
        alternateRowStyles: {
          fillColor: [248, 250, 252],
        },
        columnStyles: columnStyles,
        tableLineColor: [0, 0, 0],
        tableLineWidth: 0.2,
        margin: { top: 40, left: 14, right: 14 },
        theme: 'grid'
      });
    }
    
    doc.save('type_of_work_full_export.pdf');
    console.log('✅ Full export test completed!');
    return true;
    
  } catch (error) {
    console.error('❌ Current page full export test failed:', error);
    return false;
  }
};

/**
 * Run all full description tests
 */
export const runFullDescriptionTests = async () => {
  console.log('🚀 RUNNING FULL DESCRIPTION TESTS...');
  console.log('');
  
  const results = {
    fullDescriptionPDF: false,
    actionsExclusion: false,
    currentPageExport: false
  };
  
  // Test 1: Full description PDF
  console.log('1️⃣ Testing Full Description PDF...');
  results.fullDescriptionPDF = await testFullDescriptionPDF();
  console.log('');
  
  // Test 2: Actions column exclusion
  console.log('2️⃣ Testing Actions Column Exclusion...');
  results.actionsExclusion = testActionsColumnExclusion();
  console.log('');
  
  // Test 3: Current page export
  console.log('3️⃣ Testing Current Page Full Export...');
  results.currentPageExport = await testCurrentPageFullExport();
  console.log('');
  
  // Summary
  console.log('📊 TEST RESULTS SUMMARY:');
  console.log(`${results.fullDescriptionPDF ? '✅' : '❌'} Full Description PDF: ${results.fullDescriptionPDF ? 'PASSED' : 'FAILED'}`);
  console.log(`${results.actionsExclusion ? '✅' : '❌'} Actions Exclusion: ${results.actionsExclusion ? 'PASSED' : 'FAILED'}`);
  console.log(`${results.currentPageExport ? '✅' : '❌'} Current Page Export: ${results.currentPageExport ? 'PASSED' : 'FAILED'}`);
  
  console.log('');
  console.log('📁 Check your downloads folder for test PDF files:');
  console.log('   - full_description_test.pdf');
  console.log('   - type_of_work_full_export.pdf');
  
  return results;
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.testFullDescriptionPDF = testFullDescriptionPDF;
  window.testActionsColumnExclusion = testActionsColumnExclusion;
  window.testCurrentPageFullExport = testCurrentPageFullExport;
  window.runFullDescriptionTests = runFullDescriptionTests;
  
  console.log('🔧 Full Description Test Functions Available:');
  console.log('- window.testFullDescriptionPDF() - Test full description text');
  console.log('- window.testActionsColumnExclusion() - Test actions column removal');
  console.log('- window.testCurrentPageFullExport() - Test with real data');
  console.log('- window.runFullDescriptionTests() - Run all tests');
}
