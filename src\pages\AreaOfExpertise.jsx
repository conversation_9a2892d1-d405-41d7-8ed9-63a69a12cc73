import React, { useState, useEffect } from 'react';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ArrowPathIcon,
  MagnifyingGlassIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import {
  getAllAreaOfExpertise,
  createAreaOfExpertise,
  updateAreaOfExpertise,
  deleteAreaOfExpertise,
  restoreAreaOfExpertise,
  searchAreaOfExpertise
} from '../services/areaOfExpertiseService';

import DataTable from '../components/DataTable/DataTable';

const AreaOfExpertise = () => {
  const [areaOfExpertise, setAreaOfExpertise] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all'); // 'all', 'active', 'inactive'
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isActive: true
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    loadAreaOfExpertise();
  }, []);

  // Filter data based on status
  const getFilteredData = () => {
    let filtered = areaOfExpertise;

    if (statusFilter === 'active') {
      filtered = filtered.filter(item => item.isActive);
    } else if (statusFilter === 'inactive') {
      filtered = filtered.filter(item => !item.isActive);
    }

    return filtered.map(item => ({
      ...item,
      status: item.isActive ? 'Active' : 'Inactive',
      createdDate: new Date(item.createdAt).toLocaleDateString(),
      updatedDate: item.updatedAt ? new Date(item.updatedAt).toLocaleDateString() : '-'
    }));
  };

  const loadAreaOfExpertise = async () => {
    try {
      setLoading(true);
      const data = await getAllAreaOfExpertise();
      setAreaOfExpertise(data);
    } catch (error) {
      console.error('Error loading area of expertise:', error);
      alert('Error loading area of expertise data');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      loadAreaOfExpertise();
      return;
    }

    try {
      setLoading(true);
      const results = await searchAreaOfExpertise(searchTerm);
      setAreaOfExpertise(results);
    } catch (error) {
      console.error('Error searching area of expertise:', error);
      alert('Error searching area of expertise');
    } finally {
      setLoading(false);
    }
  };



  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      if (editingItem) {
        await updateAreaOfExpertise(editingItem.id, formData);
        alert('Area of expertise updated successfully!');
      } else {
        await createAreaOfExpertise(formData);
        alert('Area of expertise created successfully!');
      }

      setFormData({ name: '', description: '', isActive: true });
      setEditingItem(null);
      setShowAddForm(false);
      setErrors({});
      await loadAreaOfExpertise();

    } catch (error) {
      console.error('Error saving area of expertise:', error);
      alert(error.message || 'Error saving area of expertise');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    setFormData({
      name: item.name,
      description: item.description || '',
      isActive: item.isActive
    });
    setShowAddForm(true);
    setErrors({});
  };

  const handleToggleStatus = async (item) => {
    const newStatus = !item.isActive;
    const action = newStatus ? 'activate' : 'deactivate';

    if (!confirm(`Are you sure you want to ${action} "${item.name}"?`)) {
      return;
    }

    try {
      setLoading(true);
      await updateAreaOfExpertise(item.id, { ...item, isActive: newStatus });
      alert(`Area of expertise ${action}d successfully!`);
      await loadAreaOfExpertise();
    } catch (error) {
      console.error(`Error ${action}ing area of expertise:`, error);
      alert(error.message || `Error ${action}ing area of expertise`);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (item) => {
    if (!confirm(`Are you sure you want to delete "${item.name}"?`)) {
      return;
    }

    try {
      setLoading(true);
      await deleteAreaOfExpertise(item.id);
      alert('Area of expertise deleted successfully!');
      await loadAreaOfExpertise();
    } catch (error) {
      console.error('Error deleting area of expertise:', error);
      alert(error.message || 'Error deleting area of expertise');
    } finally {
      setLoading(false);
    }
  };

  // DataTable columns (matching Type of Work structure)
  const columns = [
    {
      key: 'name',
      label: 'Area of Expertise',
      sortable: true,
      render: (value) => (
        <div className="font-medium text-gray-900">{value}</div>
      )
    },
    {
      key: 'description',
      label: 'Description',
      sortable: false,
      render: (value) => (
        <div className="max-w-md text-gray-600 truncate" title={value}>
          {value}
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'Active'
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'createdDate',
      label: 'Created Date',
      sortable: true,
      render: (value) => (
        <span className="text-gray-600">{value}</span>
      )
    },
    {
      key: 'updatedDate',
      label: 'Updated Date',
      sortable: true,
      render: (value) => (
        <span className="text-gray-600">{value}</span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      filterable: false,
      render: (_, row) => (
        <div className="flex space-x-2">
          <button
            onClick={() => {
              setFormData({
                name: row.name,
                description: row.description,
                isActive: row.isActive
              });
              setEditingItem(row);
              setShowViewModal(true);
            }}
            className="text-gray-600 hover:text-gray-800"
            title="View"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleEdit(row)}
            className="text-blue-600 hover:text-blue-800"
            title="Edit"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleToggleStatus(row)}
            className={`${
              row.isActive
                ? 'text-green-600 hover:text-green-800'
                : 'text-gray-400 hover:text-gray-600'
            }`}
            title={`${row.isActive ? 'Active' : 'Inactive'} - Click to toggle to ${row.isActive ? 'Inactive' : 'Active'}`}
          >
            {row.isActive ? (
              <CheckCircleIcon className="h-4 w-4" />
            ) : (
              <XCircleIcon className="h-4 w-4" />
            )}
          </button>
          <button
            onClick={() => handleDelete(row)}
            className="text-red-600 hover:text-red-800"
            title="Delete"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ];

  // Action Icons for DataTable Header
  const actionIcons = (
    <>
      <div className="flex flex-col items-center space-y-1">
        <EyeIcon className="w-4 h-4 text-gray-600" />
        <span className="text-xs text-gray-600">View</span>
      </div>
      <div className="flex flex-col items-center space-y-1">
        <PencilIcon className="w-4 h-4 text-blue-600" />
        <span className="text-xs text-blue-600">Edit</span>
      </div>
      <div className="flex flex-col items-center space-y-1">
        <CheckCircleIcon className="w-4 h-4 text-green-600" />
        <span className="text-xs text-green-600">Toggle Active</span>
      </div>
      <div className="flex flex-col items-center space-y-1">
        <XCircleIcon className="w-4 h-4 text-gray-400" />
        <span className="text-xs text-gray-400">Toggle Inactive</span>
      </div>
      <div className="flex flex-col items-center space-y-1">
        <TrashIcon className="w-4 h-4 text-red-600" />
        <span className="text-xs text-red-600">Delete</span>
      </div>
    </>
  );

  const handleRestore = async (id) => {
    try {
      setLoading(true);
      await restoreAreaOfExpertise(id);
      alert('Area of expertise restored successfully!');
      await loadAreaOfExpertise();
    } catch (error) {
      console.error('Error restoring area of expertise:', error);
      alert(error.message || 'Error restoring area of expertise');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({ name: '', description: '', isActive: true });
    setEditingItem(null);
    setShowAddForm(false);
    setErrors({});
  };

  return (
    <div className="p-4 sm:p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Area of Expertise</h1>
          <p className="text-gray-600 text-sm sm:text-base">Manage areas of expertise for points of contact</p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="btn-primary flex items-center justify-center space-x-2 w-full sm:w-auto"
        >
          <PlusIcon className="w-4 h-4 sm:w-5 sm:h-5" />
          <span className="text-sm sm:text-base">Add New Area</span>
        </button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-4 sm:mb-6">
        {/* Total Types */}
        <div
          onClick={() => setStatusFilter('all')}
          className={`bg-white rounded-lg shadow-sm border p-4 sm:p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            statusFilter === 'all'
              ? 'border-blue-500 ring-2 ring-blue-200 bg-blue-50'
              : 'border-gray-200 hover:border-blue-300'
          }`}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-lg flex items-center justify-center ${
                statusFilter === 'all' ? 'bg-blue-200' : 'bg-blue-100'
              }`}>
                <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
            </div>
            <div className="ml-3 sm:ml-4">
              <p className="text-xs sm:text-sm font-medium text-gray-600">Total Types</p>
              <p className={`text-xl sm:text-2xl font-bold ${
                statusFilter === 'all' ? 'text-blue-700' : 'text-gray-900'
              }`}>{areaOfExpertise.length}</p>
            </div>
          </div>
          {statusFilter === 'all' && (
            <div className="mt-2 text-xs text-blue-600 font-medium">
              ✓ Showing all types
            </div>
          )}
        </div>

        {/* Active Types */}
        <div
          onClick={() => setStatusFilter('active')}
          className={`bg-white rounded-lg shadow-sm border p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            statusFilter === 'active'
              ? 'border-green-500 ring-2 ring-green-200 bg-green-50'
              : 'border-gray-200 hover:border-green-300'
          }`}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                statusFilter === 'active' ? 'bg-green-200' : 'bg-green-100'
              }`}>
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Types</p>
              <p className={`text-2xl font-bold ${
                statusFilter === 'active' ? 'text-green-700' : 'text-green-600'
              }`}>
                {areaOfExpertise.filter(item => item.isActive).length}
              </p>
            </div>
          </div>
          {statusFilter === 'active' && (
            <div className="mt-2 text-xs text-green-600 font-medium">
              ✓ Showing active types only
            </div>
          )}
        </div>

        {/* Inactive Types */}
        <div
          onClick={() => setStatusFilter('inactive')}
          className={`bg-white rounded-lg shadow-sm border p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            statusFilter === 'inactive'
              ? 'border-red-500 ring-2 ring-red-200 bg-red-50'
              : 'border-gray-200 hover:border-red-300'
          }`}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                statusFilter === 'inactive' ? 'bg-red-200' : 'bg-red-100'
              }`}>
                <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Inactive Types</p>
              <p className={`text-2xl font-bold ${
                statusFilter === 'inactive' ? 'text-red-700' : 'text-red-600'
              }`}>
                {areaOfExpertise.filter(item => !item.isActive).length}
              </p>
            </div>
          </div>
          {statusFilter === 'inactive' && (
            <div className="mt-2 text-xs text-red-600 font-medium">
              ✓ Showing inactive types only
            </div>
          )}
        </div>
      </div>

      {/* Filter Status Indicator */}
      {statusFilter !== 'all' && (
        <div className="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
            </svg>
            <span className="text-sm font-medium text-blue-800">
              Showing {statusFilter} types only ({getFilteredData().length} of {areaOfExpertise.length} total)
            </span>
          </div>
          <button
            onClick={() => setStatusFilter('all')}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            Clear Filter
          </button>
        </div>
      )}

      {/* Data Table */}
      <div className="card">
        <DataTable
          data={getFilteredData()}
          columns={columns}
          title="Area of Expertise"
          defaultPageSize={10}
          pageSizeOptions={[10, 20, 50, 100]}
          enableExport={true}
          enableColumnToggle={true}
          enableFiltering={true}
          enableSorting={true}
          loading={loading}
          actionIcons={actionIcons}
        />
      </div>

      {/* Add/Edit Form Modal */}
      {showAddForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={resetForm}></div>
            <div className="relative bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingItem ? 'Edit Area of Expertise' : 'Add New Area of Expertise'}
              </h3>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className={`input-field ${errors.name ? 'border-red-300' : ''}`}
                    placeholder="e.g., Patent Law, Trademark Law"
                  />
                  {errors.name && <p className="text-red-600 text-sm mt-1">{errors.name}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className={`input-field ${errors.description ? 'border-red-300' : ''}`}
                    rows="3"
                    placeholder="Brief description of this area of expertise"
                  />
                  {errors.description && <p className="text-red-600 text-sm mt-1">{errors.description}</p>}
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="isActive" className="ml-2 text-sm text-gray-700">
                    Active
                  </label>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                    disabled={loading}
                  >
                    {loading ? 'Saving...' : (editingItem ? 'Update' : 'Create')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* View Modal */}
      {showViewModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => {
              setShowViewModal(false);
              setEditingItem(null);
              setFormData({ name: '', description: '', isActive: true });
            }}></div>
            <div className="relative bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                View Area of Expertise
              </h3>

              <div className="space-y-4">
                {/* Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Name
                  </label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                    {formData.name}
                  </div>
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 min-h-[80px]">
                    {formData.description}
                  </div>
                </div>

                {/* Status and Date Info */}
                <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
                  <p><strong>Status:</strong> {formData.isActive ? 'Active' : 'Inactive'}</p>
                  {editingItem && (
                    <>
                      <p><strong>Created:</strong> {new Date(editingItem.createdAt).toLocaleDateString()}</p>
                      {editingItem.updatedAt && editingItem.updatedAt !== editingItem.createdAt && (
                        <p><strong>Updated:</strong> {new Date(editingItem.updatedAt).toLocaleDateString()}</p>
                      )}
                    </>
                  )}
                </div>

                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={() => {
                      setShowViewModal(false);
                      setEditingItem(null);
                      setFormData({ name: '', description: '', isActive: true });
                    }}
                    className="btn-secondary"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AreaOfExpertise;
