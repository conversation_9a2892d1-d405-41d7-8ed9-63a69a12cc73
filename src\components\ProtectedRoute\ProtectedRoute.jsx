import { useEffect, useState } from 'react';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import { getCurrentUser, isAuthenticated, clearAllAuthData } from '../../services/enhancedAuthService';

const ProtectedRoute = ({ children, requiredRole = null }) => {
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [authBlocked, setAuthBlocked] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const checkAuth = () => {
      try {
        console.log('🔒 ProtectedRoute: STRICT authentication check...');
        const authenticated = isAuthenticated();
        console.log('🔒 ProtectedRoute: Authentication result:', authenticated);

        if (authenticated) {
          const currentUser = getCurrentUser();
          console.log('🔒 ProtectedRoute: User verified:', currentUser?.username);

          if (currentUser) {
            setUser(currentUser);
            setAuthBlocked(false);
          } else {
            console.log('🔒 ProtectedRoute: No user data - BLOCKING');
            setUser(null);
            setAuthBlocked(true);
          }
        } else {
          console.log('🔒 ProtectedRoute: Not authenticated - BLOCKING');
          setUser(null);
          setAuthBlocked(true);
        }
      } catch (error) {
        console.error('🔒 ProtectedRoute: Auth check error - BLOCKING:', error);
        setUser(null);
        setAuthBlocked(true);
      } finally {
        setLoading(false);
      }
    };

    // Immediate strict auth check
    checkAuth();

    // Prevent any navigation without auth
    const handleBeforeUnload = () => {
      if (!isAuthenticated()) {
        console.log('🔒 ProtectedRoute: Clearing data on unload');
        clearAllAuthData();
      }
    };

    // Block browser navigation if not authenticated
    const handlePopState = (event) => {
      console.log('🔒 ProtectedRoute: Browser navigation - checking auth...');

      if (!isAuthenticated()) {
        console.log('🔒 ProtectedRoute: BLOCKING browser navigation - not authenticated');
        event.preventDefault();
        event.stopPropagation();
        clearAllAuthData();
        window.location.replace('/login');
        return false;
      }

      // Re-verify auth on navigation
      checkAuth();
    };

    // Add strict event listeners
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('popstate', handlePopState, true); // Use capture phase

    // Cleanup
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('popstate', handlePopState, true);
    };
  }, [navigate, location.pathname]);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Verifying access permissions...</p>
        </div>
      </div>
    );
  }

  // BLOCK access if authentication failed
  if (authBlocked || !user) {
    console.log('🔒 ProtectedRoute: ACCESS BLOCKED - redirecting to login');
    clearAllAuthData();

    // Force immediate redirect without allowing any rendering
    setTimeout(() => {
      window.location.replace('/login');
    }, 0);

    return (
      <div className="min-h-screen bg-red-50 flex items-center justify-center">
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-red-900 mb-2">Access Denied</h2>
          <p className="text-red-700 mb-4">Authentication required. Redirecting to login...</p>
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-600 mx-auto"></div>
        </div>
      </div>
    );
  }

  // Check role-based access
  if (requiredRole) {
    // Convert old role format to new format
    const userRole = user.userType || user.role;
    const hasAccess = checkRoleAccess(userRole, requiredRole);

    if (!hasAccess) {
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
            <p className="text-gray-600 mb-4">You don't have permission to access this page.</p>
            <p className="text-sm text-gray-500">Required role: {requiredRole} | Your role: {userRole}</p>
          </div>
        </div>
      );
    }
  }

  // Helper function to check role access
  function checkRoleAccess(userRole, requiredRole) {
    // Admin access
    if (requiredRole === 'ADMIN') {
      return userRole === 'admin' || userRole === 'ADMIN';
    }

    // Sub-admin access (admin can also access)
    if (requiredRole === 'SUB_ADMIN') {
      return userRole === 'admin' || userRole === 'ADMIN' || userRole === 'subadmin' || userRole === 'SUB_ADMIN';
    }

    // Vendor access
    if (requiredRole === 'VENDOR') {
      return userRole === 'vendor' || userRole === 'VENDOR';
    }

    return false;
  }

  // Render protected content
  return children;
};

export default ProtectedRoute;
