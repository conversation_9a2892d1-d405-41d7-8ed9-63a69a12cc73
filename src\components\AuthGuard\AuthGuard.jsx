import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { isAuthenticated, clearAllAuthData } from '../../services/enhancedAuthService';

const AuthGuard = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    // Bulletproof authentication guard
    const handleAuthCheck = () => {
      const isPublicRoute = location.pathname === '/login' ||
                           location.pathname.startsWith('/test-');

      console.log('🔒 AuthGuard: Checking route:', location.pathname, 'Public:', isPublicRoute);

      if (!isPublicRoute) {
        const authenticated = isAuthenticated();
        console.log('🔒 AuthGuard: Authentication status:', authenticated);

        if (!authenticated) {
          console.log('🔒 AuthGuard: BLOCKING access - redirecting to login');
          clearAllAuthData();

          // Force immediate redirect
          window.location.replace('/login');
          return false;
        }
      }

      setIsChecking(false);
      return true;
    };

    // Immediate auth check
    const canProceed = handleAuthCheck();

    if (!canProceed) {
      return; // Don't set up other listeners if redirecting
    }

    // Continuous monitoring
    const authCheckInterval = setInterval(() => {
      const isPublicRoute = location.pathname === '/login' ||
                           location.pathname.startsWith('/test-');

      if (!isPublicRoute && !isAuthenticated()) {
        console.log('🔒 AuthGuard: Session lost during use, redirecting');
        clearAllAuthData();
        window.location.replace('/login');
      }
    }, 5000); // Check every 5 seconds

    // Browser navigation guard
    const handlePopState = (event) => {
      console.log('🔒 AuthGuard: Browser navigation detected');
      event.preventDefault();

      const isPublicRoute = location.pathname === '/login' ||
                           location.pathname.startsWith('/test-');

      if (!isPublicRoute && !isAuthenticated()) {
        console.log('🔒 AuthGuard: BLOCKING browser navigation - not authenticated');
        clearAllAuthData();
        window.location.replace('/login');
      }
    };

    // Page visibility guard
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        const isPublicRoute = location.pathname === '/login' ||
                             location.pathname.startsWith('/test-');

        if (!isPublicRoute && !isAuthenticated()) {
          console.log('🔒 AuthGuard: BLOCKING on visibility change - session expired');
          clearAllAuthData();
          window.location.replace('/login');
        }
      }
    };

    // Storage change guard (detects logout in other tabs)
    const handleStorageChange = (event) => {
      if (event.key === 'innoventory_auth' && !event.newValue) {
        console.log('🔒 AuthGuard: Auth cleared in another tab, redirecting');
        window.location.replace('/login');
      }
    };

    // Add all event listeners
    window.addEventListener('popstate', handlePopState);
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('storage', handleStorageChange);

    // Cleanup
    return () => {
      clearInterval(authCheckInterval);
      window.removeEventListener('popstate', handlePopState);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [navigate, location.pathname]);

  // Show loading while checking authentication
  if (isChecking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Verifying authentication...</p>
        </div>
      </div>
    );
  }

  return children;
};

export default AuthGuard;
