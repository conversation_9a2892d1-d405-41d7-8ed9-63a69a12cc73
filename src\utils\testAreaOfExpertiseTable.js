/**
 * Test Area of Expertise Table Structure
 * Verifies that the Area of Expertise page uses DataTable like Type of Work
 */

/**
 * Test if Area of Expertise page uses DataTable component
 */
export const testAreaOfExpertiseDataTable = () => {
  try {
    console.log('🔧 Testing Area of Expertise DataTable implementation...');
    console.log('');

    const currentPath = window.location.pathname;
    if (!currentPath.includes('area-of-expertise')) {
      console.log('⚠️ Please navigate to the Area of Expertise page first');
      return { success: false, message: 'Not on Area of Expertise page' };
    }

    // Check for DataTable structure
    const dataTableCard = document.querySelector('.card');
    const dataTableHeader = document.querySelector('.card h3');
    const dataTableTable = document.querySelector('.card table');

    console.log('📊 DataTable Structure Analysis:');
    console.log(`   Card container: ${dataTableCard ? '✅ FOUND' : '❌ NOT FOUND'}`);
    console.log(`   Table header: ${dataTableHeader ? '✅ FOUND' : '❌ NOT FOUND'}`);
    console.log(`   Table element: ${dataTableTable ? '✅ FOUND' : '❌ NOT FOUND'}`);

    if (dataTableHeader) {
      console.log(`   Header text: "${dataTableHeader.textContent}"`);
    }

    // Check for Action Icons
    const actionIcons = document.querySelector('[class*="Action Icons"]') || 
                       document.querySelector('span:contains("Action Icons:")');
    
    let actionIconsFound = false;
    const allSpans = document.querySelectorAll('span');
    allSpans.forEach(span => {
      if (span.textContent.includes('Action Icons:')) {
        actionIconsFound = true;
      }
    });

    console.log(`   Action Icons: ${actionIconsFound ? '✅ FOUND' : '❌ NOT FOUND'}`);

    // Check for status filter buttons
    const statusButtons = document.querySelectorAll('button');
    let hasStatusFilter = false;
    statusButtons.forEach(button => {
      if (button.textContent.includes('All (') || 
          button.textContent.includes('Active (') || 
          button.textContent.includes('Inactive (')) {
        hasStatusFilter = true;
      }
    });

    console.log(`   Status Filter: ${hasStatusFilter ? '✅ FOUND' : '❌ NOT FOUND'}`);

    // Check for table columns
    const tableHeaders = document.querySelectorAll('th');
    const expectedColumns = ['Area of Expertise', 'Description', 'Status', 'Created Date', 'Updated Date', 'Actions'];
    let columnsFound = 0;

    console.log('');
    console.log('📋 Table Columns Analysis:');
    tableHeaders.forEach((header, index) => {
      const headerText = header.textContent.trim();
      console.log(`   Column ${index + 1}: "${headerText}"`);
      
      if (expectedColumns.some(col => headerText.includes(col))) {
        columnsFound++;
      }
    });

    console.log(`   Expected columns found: ${columnsFound}/${expectedColumns.length}`);

    // Overall assessment
    const hasDataTable = dataTableCard && dataTableHeader && dataTableTable;
    const hasProperStructure = hasDataTable && actionIconsFound && hasStatusFilter;
    const hasCorrectColumns = columnsFound >= 4; // At least 4 main columns

    console.log('');
    console.log('🎯 Overall Assessment:');
    console.log(`   Uses DataTable: ${hasDataTable ? '✅ YES' : '❌ NO'}`);
    console.log(`   Has Action Icons: ${actionIconsFound ? '✅ YES' : '❌ NO'}`);
    console.log(`   Has Status Filter: ${hasStatusFilter ? '✅ YES' : '❌ NO'}`);
    console.log(`   Has Correct Columns: ${hasCorrectColumns ? '✅ YES' : '❌ NO'}`);

    const matchesTypeOfWork = hasProperStructure && hasCorrectColumns;

    console.log('');
    console.log(`🏆 MATCHES TYPE OF WORK STRUCTURE: ${matchesTypeOfWork ? '✅ YES' : '❌ NO'}`);

    return {
      success: true,
      hasDataTable: hasDataTable,
      hasActionIcons: actionIconsFound,
      hasStatusFilter: hasStatusFilter,
      columnsFound: columnsFound,
      matchesTypeOfWork: matchesTypeOfWork
    };

  } catch (error) {
    console.error('💥 Area of Expertise DataTable test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test description column tooltips (should match Type of Work)
 */
export const testAreaOfExpertiseTooltips = () => {
  try {
    console.log('💬 Testing Area of Expertise description tooltips...');
    console.log('');

    // Look for description cells with title attribute
    const descriptionCells = document.querySelectorAll('td .truncate[title]');
    console.log(`💬 Description cells with title tooltips: ${descriptionCells.length}`);

    if (descriptionCells.length > 0) {
      descriptionCells.forEach((cell, index) => {
        if (index < 3) { // Show first 3 examples
          const title = cell.getAttribute('title');
          const text = cell.textContent.trim();
          console.log(`   Example ${index + 1}: "${text}" → tooltip: "${title.substring(0, 40)}..."`);
        }
      });

      console.log('');
      console.log('✅ TOOLTIPS WORKING - Matches Type of Work implementation');
      return { success: true, tooltipsWorking: true, tooltipCount: descriptionCells.length };
    } else {
      console.log('❌ No description tooltips found');
      return { success: true, tooltipsWorking: false, tooltipCount: 0 };
    }

  } catch (error) {
    console.error('💥 Area of Expertise tooltips test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Compare Area of Expertise with Type of Work page
 */
export const compareAreaOfExpertiseWithTypeOfWork = () => {
  try {
    console.log('🔄 Comparing Area of Expertise with Type of Work structure...');
    console.log('');

    const currentPath = window.location.pathname;
    console.log(`📍 Current page: ${currentPath}`);

    if (!currentPath.includes('area-of-expertise')) {
      console.log('⚠️ Please navigate to Area of Expertise page for comparison');
      return { success: false, message: 'Wrong page for comparison' };
    }

    // Test DataTable implementation
    const dataTableResult = testAreaOfExpertiseDataTable();
    
    // Test tooltips
    const tooltipsResult = testAreaOfExpertiseTooltips();

    console.log('');
    console.log('📊 COMPARISON SUMMARY:');
    console.log('');
    console.log('🔧 DataTable Implementation:');
    console.log(`   Status: ${dataTableResult.success ? '✅ TESTED' : '❌ FAILED'}`);
    if (dataTableResult.matchesTypeOfWork !== undefined) {
      console.log(`   Matches Type of Work: ${dataTableResult.matchesTypeOfWork ? '✅ YES' : '❌ NO'}`);
    }

    console.log('');
    console.log('💬 Description Tooltips:');
    console.log(`   Status: ${tooltipsResult.success ? '✅ TESTED' : '❌ FAILED'}`);
    if (tooltipsResult.tooltipsWorking !== undefined) {
      console.log(`   Working like Type of Work: ${tooltipsResult.tooltipsWorking ? '✅ YES' : '❌ NO'}`);
    }

    const overallMatch = dataTableResult.matchesTypeOfWork && tooltipsResult.tooltipsWorking;

    console.log('');
    console.log(`🏆 OVERALL MATCH WITH TYPE OF WORK: ${overallMatch ? '✅ PERFECT MATCH' : '❌ NEEDS WORK'}`);

    if (overallMatch) {
      console.log('');
      console.log('🎉 SUCCESS! Area of Expertise now uses the same table structure as Type of Work:');
      console.log('   ✅ DataTable component with proper columns');
      console.log('   ✅ Action Icons in header');
      console.log('   ✅ Status filter buttons');
      console.log('   ✅ Native HTML title tooltips for descriptions');
      console.log('   ✅ Consistent styling and functionality');
    }

    return {
      success: true,
      dataTableMatch: dataTableResult.matchesTypeOfWork,
      tooltipsMatch: tooltipsResult.tooltipsWorking,
      overallMatch: overallMatch
    };

  } catch (error) {
    console.error('💥 Comparison test failed:', error);
    return { success: false, error: error.message };
  }
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.testAreaOfExpertiseDataTable = testAreaOfExpertiseDataTable;
  window.testAreaOfExpertiseTooltips = testAreaOfExpertiseTooltips;
  window.compareAreaOfExpertiseWithTypeOfWork = compareAreaOfExpertiseWithTypeOfWork;
  
  console.log('🔧 Area of Expertise Test Functions Available:');
  console.log('- window.testAreaOfExpertiseDataTable() - Test DataTable implementation');
  console.log('- window.testAreaOfExpertiseTooltips() - Test description tooltips');
  console.log('- window.compareAreaOfExpertiseWithTypeOfWork() - Compare with Type of Work');
}
