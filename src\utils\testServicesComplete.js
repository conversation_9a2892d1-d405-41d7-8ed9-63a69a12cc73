/**
 * Complete Services & Categories Test Suite
 * Tests all functionality for both Services and Service Categories pages
 */

/**
 * Test tab order and navigation
 */
export const testTabOrder = () => {
  try {
    console.log('🔄 Testing tab order and navigation...');
    console.log('');

    // Check if we're on the Services page
    const currentPath = window.location.pathname;
    if (!currentPath.includes('services')) {
      console.log('⚠️ Please navigate to the Services page first');
      return { success: false, message: 'Not on Services page' };
    }

    // Look for tab navigation
    const tabs = document.querySelectorAll('nav button');
    console.log(`📋 Navigation tabs found: ${tabs.length}`);

    if (tabs.length < 2) {
      console.log('❌ Expected at least 2 tabs, found:', tabs.length);
      return { success: false, message: 'Tabs not found' };
    }

    const tabOrder = [];
    tabs.forEach((tab, index) => {
      const tabText = tab.textContent.trim();
      tabOrder.push(tabText);
      console.log(`Tab ${index + 1}: "${tabText}"`);
    });

    // Check if Categories comes first
    const categoriesFirst = tabOrder[0].includes('Categories');
    const servicesSecond = tabOrder[1].includes('Services');

    console.log('');
    console.log('📊 Tab Order Analysis:');
    console.log(`   First tab: ${tabOrder[0]} ${categoriesFirst ? '✅' : '❌'}`);
    console.log(`   Second tab: ${tabOrder[1]} ${servicesSecond ? '✅' : '❌'}`);
    console.log(`   Correct order: ${categoriesFirst && servicesSecond ? '✅' : '❌'}`);

    return {
      success: true,
      tabOrder: tabOrder,
      correctOrder: categoriesFirst && servicesSecond
    };

  } catch (error) {
    console.error('💥 Tab order test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test action icons legend
 */
export const testActionIconsLegend = () => {
  try {
    console.log('🔧 Testing action icons legend...');
    console.log('');

    // Look for action icons legend
    const legend = document.querySelector('.bg-gray-50');
    
    if (!legend) {
      console.log('❌ Action icons legend not found');
      return { success: false, message: 'Legend not found' };
    }

    console.log('✅ Action icons legend found');

    // Check for legend items
    const legendItems = legend.querySelectorAll('.flex.items-center.space-x-2');
    console.log(`📋 Legend items found: ${legendItems.length}`);

    const expectedIcons = ['View', 'Edit', 'Toggle Active', 'Toggle Inactive', 'Delete'];
    const foundIcons = [];

    legendItems.forEach((item, index) => {
      const text = item.textContent.trim();
      if (text !== 'Action Icons:') {
        foundIcons.push(text);
        console.log(`   Icon ${index}: "${text}"`);
      }
    });

    console.log('');
    console.log('🔧 Legend Analysis:');
    console.log(`   Expected icons: ${expectedIcons.length}`);
    console.log(`   Found icons: ${foundIcons.length}`);

    const allIconsPresent = expectedIcons.every(icon => 
      foundIcons.some(found => found.includes(icon.split(' ')[0]))
    );

    console.log(`   All icons present: ${allIconsPresent ? '✅' : '❌'}`);

    return {
      success: true,
      legendFound: true,
      expectedIcons: expectedIcons,
      foundIcons: foundIcons,
      allIconsPresent: allIconsPresent
    };

  } catch (error) {
    console.error('💥 Action icons legend test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test statistics cards functionality
 */
export const testStatisticsCards = () => {
  try {
    console.log('📊 Testing statistics cards functionality...');
    console.log('');

    // Look for statistics cards
    const statsCards = document.querySelectorAll('.grid .cursor-pointer');
    console.log(`📊 Statistics cards found: ${statsCards.length}`);

    if (statsCards.length < 3) {
      console.log('❌ Expected 3 statistics cards, found:', statsCards.length);
      return { success: false, message: 'Statistics cards not found' };
    }

    // Extract statistics from cards
    const stats = {
      total: null,
      active: null,
      inactive: null
    };

    statsCards.forEach((card, index) => {
      const label = card.querySelector('.text-sm.font-medium')?.textContent?.trim();
      const value = card.querySelector('.text-2xl.font-bold')?.textContent?.trim();
      
      console.log(`Card ${index + 1}: ${label} = ${value}`);
      
      if (label?.includes('Total')) {
        stats.total = parseInt(value) || 0;
      } else if (label?.includes('Active')) {
        stats.active = parseInt(value) || 0;
      } else if (label?.includes('Inactive')) {
        stats.inactive = parseInt(value) || 0;
      }
    });

    console.log('');
    console.log('📊 Statistics Summary:');
    console.log(`   Total: ${stats.total}`);
    console.log(`   Active: ${stats.active}`);
    console.log(`   Inactive: ${stats.inactive}`);

    // Verify math
    const calculatedTotal = stats.active + stats.inactive;
    const mathCorrect = stats.total === calculatedTotal;

    console.log('');
    console.log('🔢 Math Verification:');
    console.log(`   Active + Inactive = ${stats.active} + ${stats.inactive} = ${calculatedTotal}`);
    console.log(`   Total from card = ${stats.total}`);
    console.log(`   Math correct: ${mathCorrect ? '✅' : '❌'}`);

    return {
      success: true,
      stats: stats,
      mathCorrect: mathCorrect
    };

  } catch (error) {
    console.error('💥 Statistics cards test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test complete action icons functionality
 */
export const testCompleteActionIcons = () => {
  try {
    console.log('🔧 Testing complete action icons functionality...');
    console.log('');

    // Look for table rows with actions
    const tableRows = document.querySelectorAll('tbody tr');
    console.log(`📋 Table rows found: ${tableRows.length}`);

    if (tableRows.length === 0) {
      console.log('❌ No table rows found');
      return { success: false, message: 'No data in table' };
    }

    const actionResults = {
      viewIcons: 0,
      editIcons: 0,
      toggleIcons: 0,
      deleteIcons: 0,
      totalRows: tableRows.length
    };

    tableRows.forEach((row, index) => {
      const actionButtons = row.querySelectorAll('button');
      console.log(`Row ${index + 1}: ${actionButtons.length} action buttons`);

      actionButtons.forEach((button, btnIndex) => {
        const title = button.getAttribute('title') || '';
        
        if (title.includes('View')) {
          actionResults.viewIcons++;
        } else if (title.includes('Edit')) {
          actionResults.editIcons++;
        } else if (title.includes('Activate') || title.includes('Deactivate')) {
          actionResults.toggleIcons++;
        } else if (title.includes('Delete')) {
          actionResults.deleteIcons++;
        }
      });
    });

    console.log('');
    console.log('🔧 Action Icons Summary:');
    console.log(`   View icons: ${actionResults.viewIcons}/${actionResults.totalRows}`);
    console.log(`   Edit icons: ${actionResults.editIcons}/${actionResults.totalRows}`);
    console.log(`   Toggle icons: ${actionResults.toggleIcons}/${actionResults.totalRows}`);
    console.log(`   Delete icons: ${actionResults.deleteIcons}/${actionResults.totalRows}`);

    const expectedIcons = actionResults.totalRows;
    const allIconsPresent = actionResults.viewIcons === expectedIcons &&
                           actionResults.editIcons === expectedIcons &&
                           actionResults.toggleIcons === expectedIcons &&
                           actionResults.deleteIcons === expectedIcons;

    console.log('');
    console.log(`🎯 Action Icons Result: ${allIconsPresent ? '✅ ALL PRESENT' : '⚠️ SOME MISSING'}`);

    return {
      success: true,
      actionResults: actionResults,
      allIconsPresent: allIconsPresent
    };

  } catch (error) {
    console.error('💥 Action icons test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test Updated At column
 */
export const testUpdatedAtColumn = () => {
  try {
    console.log('📅 Testing Updated At column...');
    console.log('');

    // Check for Updated column header
    const headers = document.querySelectorAll('th');
    let updatedHeader = null;
    
    headers.forEach((header, index) => {
      if (header.textContent.includes('Updated')) {
        updatedHeader = header;
      }
    });

    console.log(`📋 Updated column header: ${updatedHeader ? '✅ FOUND' : '❌ MISSING'}`);

    if (!updatedHeader) {
      return {
        success: false,
        message: 'Updated At column header not found'
      };
    }

    // Check for updated date data in table rows
    const tableRows = document.querySelectorAll('tbody tr');
    let updatedDateCells = 0;
    let validDates = 0;
    let naDates = 0;

    tableRows.forEach((row, index) => {
      const cells = row.querySelectorAll('td');
      // Find the Updated column (look for cells with date format or N/A)
      cells.forEach(cell => {
        const text = cell.textContent.trim();
        if (text === 'N/A' || (text.includes('/') && text.length >= 8)) {
          // This might be the updated column
          const prevCell = cell.previousElementSibling;
          if (prevCell && (prevCell.textContent.includes('/') || prevCell.textContent.includes('Created'))) {
            updatedDateCells++;
            if (text === 'N/A') {
              naDates++;
            } else {
              validDates++;
            }
          }
        }
      });
    });

    console.log('');
    console.log('📅 Updated Date Analysis:');
    console.log(`   Total rows: ${tableRows.length}`);
    console.log(`   Updated cells found: ${updatedDateCells}`);
    console.log(`   Valid dates: ${validDates}`);
    console.log(`   N/A dates: ${naDates}`);

    const columnWorking = updatedDateCells > 0;

    console.log('');
    console.log(`📅 Updated Column Result: ${columnWorking ? '✅ WORKING' : '❌ ISSUES FOUND'}`);

    return {
      success: true,
      updatedColumn: {
        headerFound: !!updatedHeader,
        totalRows: tableRows.length,
        updatedCells: updatedDateCells,
        validDates: validDates,
        naDates: naDates,
        columnWorking: columnWorking
      }
    };

  } catch (error) {
    console.error('💥 Updated At column test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Run complete Services & Categories test suite
 */
export const runCompleteServicesTests = () => {
  console.log('🚀 RUNNING COMPLETE SERVICES & CATEGORIES TEST SUITE...');
  console.log('');

  const results = {};

  // Test 1: Tab order
  console.log('1️⃣ Testing Tab Order...');
  results.tabOrder = testTabOrder();
  console.log('');

  // Test 2: Action icons legend
  console.log('2️⃣ Testing Action Icons Legend...');
  results.legend = testActionIconsLegend();
  console.log('');

  // Test 3: Statistics cards
  console.log('3️⃣ Testing Statistics Cards...');
  results.statistics = testStatisticsCards();
  console.log('');

  // Test 4: Action icons
  console.log('4️⃣ Testing Action Icons...');
  results.actions = testCompleteActionIcons();
  console.log('');

  // Test 5: Updated At column
  console.log('5️⃣ Testing Updated At Column...');
  results.updatedColumn = testUpdatedAtColumn();
  console.log('');

  // Summary
  setTimeout(() => {
    console.log('📋 COMPLETE SERVICES TEST SUMMARY:');
    console.log('');
    console.log('1️⃣ Tab Order:');
    console.log(`   Status: ${results.tabOrder.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (results.tabOrder.correctOrder !== undefined) {
      console.log(`   - Categories first: ${results.tabOrder.correctOrder ? '✅' : '❌'}`);
    }

    console.log('');
    console.log('2️⃣ Action Icons Legend:');
    console.log(`   Status: ${results.legend.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (results.legend.allIconsPresent !== undefined) {
      console.log(`   - All icons present: ${results.legend.allIconsPresent ? '✅' : '❌'}`);
    }

    console.log('');
    console.log('3️⃣ Statistics Cards:');
    console.log(`   Status: ${results.statistics.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (results.statistics.mathCorrect !== undefined) {
      console.log(`   - Math correct: ${results.statistics.mathCorrect ? '✅' : '❌'}`);
    }

    console.log('');
    console.log('4️⃣ Action Icons:');
    console.log(`   Status: ${results.actions.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (results.actions.allIconsPresent !== undefined) {
      console.log(`   - All icons present: ${results.actions.allIconsPresent ? '✅' : '❌'}`);
    }

    console.log('');
    console.log('5️⃣ Updated At Column:');
    console.log(`   Status: ${results.updatedColumn.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (results.updatedColumn.updatedColumn) {
      console.log(`   - Column working: ${results.updatedColumn.updatedColumn.columnWorking ? '✅' : '❌'}`);
    }

    console.log('');
    console.log('🎉 COMPLETE SERVICES TEST SUITE FINISHED!');
    console.log('');
    console.log('📋 Next Steps:');
    console.log('1. Navigate between Categories and Services tabs');
    console.log('2. Test clicking statistics cards for filtering');
    console.log('3. Test all action icons (View, Edit, Toggle, Delete)');
    console.log('4. Verify Updated At column shows proper dates');
  }, 1000);

  return results;
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.testTabOrder = testTabOrder;
  window.testActionIconsLegend = testActionIconsLegend;
  window.testStatisticsCards = testStatisticsCards;
  window.testCompleteActionIcons = testCompleteActionIcons;
  window.testUpdatedAtColumn = testUpdatedAtColumn;
  window.runCompleteServicesTests = runCompleteServicesTests;
  
  console.log('🔧 Complete Services Test Functions Available:');
  console.log('- window.testTabOrder() - Test tab order');
  console.log('- window.testActionIconsLegend() - Test action icons legend');
  console.log('- window.testStatisticsCards() - Test statistics cards');
  console.log('- window.testCompleteActionIcons() - Test action icons');
  console.log('- window.testUpdatedAtColumn() - Test updated column');
  console.log('- window.runCompleteServicesTests() - Run all tests');
}
