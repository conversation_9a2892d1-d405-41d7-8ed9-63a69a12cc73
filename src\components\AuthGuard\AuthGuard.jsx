import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { isAuthenticated, clearAllAuthData } from '../../services/enhancedAuthService';

const AuthGuard = ({ children }) => {
  const location = useLocation();

  useEffect(() => {
    // Add a small delay to allow authentication to complete
    const checkAuth = setTimeout(() => {
      const isPublicRoute = location.pathname === '/login' ||
                           location.pathname.startsWith('/test-');

      // Only check auth for protected routes
      if (!isPublicRoute && !isAuthenticated()) {
        console.log('🔐 AuthGuard: Not authenticated, redirecting to login');
        window.location.replace('/login');
      }
    }, 100); // Small delay to allow login to complete

    // Handle browser back button after logout
    const handlePopState = () => {
      setTimeout(() => {
        const currentIsPublic = window.location.pathname === '/login' ||
                               window.location.pathname.startsWith('/test-');

        if (!currentIsPublic && !isAuthenticated()) {
          console.log('🔐 AuthGuard: Back button blocked, redirecting to login');
          window.location.replace('/login');
        }
      }, 100);
    };

    // Storage change guard (detects logout in other tabs)
    const handleStorageChange = (event) => {
      if (event.key === 'innoventory_auth' && !event.newValue) {
        console.log('🔐 AuthGuard: Logged out in another tab, redirecting');
        window.location.replace('/login');
      }
    };

    window.addEventListener('popstate', handlePopState);
    window.addEventListener('storage', handleStorageChange);

    return () => {
      clearTimeout(checkAuth);
      window.removeEventListener('popstate', handlePopState);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [location.pathname]);

  return children;
};

export default AuthGuard;
