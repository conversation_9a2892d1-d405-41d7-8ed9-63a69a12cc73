/**
 * Test Services Page Functionality
 * Tests statistics cards, action icons, and updated at column
 */

/**
 * Test statistics cards functionality
 */
export const testServicesStatistics = () => {
  try {
    console.log('🧪 Testing Services page statistics cards...');
    console.log('');

    // Check if we're on the Services page
    const currentPath = window.location.pathname;
    if (!currentPath.includes('services')) {
      console.log('⚠️ Please navigate to the Services page first');
      return { success: false, message: 'Not on Services page' };
    }

    // Look for statistics cards
    const statsCards = document.querySelectorAll('.grid .cursor-pointer');
    console.log(`📊 Statistics cards found: ${statsCards.length}`);

    if (statsCards.length < 3) {
      console.log('❌ Expected 3 statistics cards, found:', statsCards.length);
      return { success: false, message: 'Statistics cards not found' };
    }

    // Extract statistics from cards
    const stats = {
      total: null,
      active: null,
      inactive: null
    };

    statsCards.forEach((card, index) => {
      const label = card.querySelector('.text-sm.font-medium')?.textContent?.trim();
      const value = card.querySelector('.text-2xl.font-bold')?.textContent?.trim();
      
      console.log(`Card ${index + 1}: ${label} = ${value}`);
      
      if (label?.includes('Total')) {
        stats.total = parseInt(value) || 0;
      } else if (label?.includes('Active')) {
        stats.active = parseInt(value) || 0;
      } else if (label?.includes('Inactive')) {
        stats.inactive = parseInt(value) || 0;
      }
    });

    console.log('');
    console.log('📊 Services Statistics:');
    console.log(`   Total Services: ${stats.total}`);
    console.log(`   Active Services: ${stats.active}`);
    console.log(`   Inactive Services: ${stats.inactive}`);

    // Verify math
    const calculatedTotal = stats.active + stats.inactive;
    const mathCorrect = stats.total === calculatedTotal;

    console.log('');
    console.log('🔢 Math Verification:');
    console.log(`   Active + Inactive = ${stats.active} + ${stats.inactive} = ${calculatedTotal}`);
    console.log(`   Total from card = ${stats.total}`);
    console.log(`   Math correct: ${mathCorrect ? '✅' : '❌'}`);

    return {
      success: true,
      stats: stats,
      mathCorrect: mathCorrect
    };

  } catch (error) {
    console.error('💥 Services statistics test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test action icons functionality
 */
export const testActionIcons = () => {
  try {
    console.log('🔧 Testing Services action icons...');
    console.log('');

    // Look for table rows with actions
    const tableRows = document.querySelectorAll('tbody tr');
    console.log(`📋 Table rows found: ${tableRows.length}`);

    if (tableRows.length === 0) {
      console.log('❌ No table rows found');
      return { success: false, message: 'No data in table' };
    }

    const actionResults = {
      viewIcons: 0,
      editIcons: 0,
      toggleIcons: 0,
      deleteIcons: 0,
      totalRows: tableRows.length
    };

    tableRows.forEach((row, index) => {
      const actionButtons = row.querySelectorAll('button');
      console.log(`Row ${index + 1}: ${actionButtons.length} action buttons`);

      actionButtons.forEach((button, btnIndex) => {
        const title = button.getAttribute('title') || '';
        const iconClass = button.querySelector('svg')?.getAttribute('class') || '';
        
        console.log(`   Button ${btnIndex + 1}: "${title}"`);

        if (title.includes('View')) {
          actionResults.viewIcons++;
        } else if (title.includes('Edit')) {
          actionResults.editIcons++;
        } else if (title.includes('Activate') || title.includes('Deactivate')) {
          actionResults.toggleIcons++;
        } else if (title.includes('Delete')) {
          actionResults.deleteIcons++;
        }
      });
    });

    console.log('');
    console.log('🔧 Action Icons Summary:');
    console.log(`   View icons: ${actionResults.viewIcons}/${actionResults.totalRows}`);
    console.log(`   Edit icons: ${actionResults.editIcons}/${actionResults.totalRows}`);
    console.log(`   Toggle icons: ${actionResults.toggleIcons}/${actionResults.totalRows}`);
    console.log(`   Delete icons: ${actionResults.deleteIcons}/${actionResults.totalRows}`);

    const expectedIcons = actionResults.totalRows;
    const allIconsPresent = actionResults.viewIcons === expectedIcons &&
                           actionResults.editIcons === expectedIcons &&
                           actionResults.toggleIcons === expectedIcons &&
                           actionResults.deleteIcons === expectedIcons;

    console.log('');
    console.log(`🎯 Action Icons Result: ${allIconsPresent ? '✅ ALL PRESENT' : '⚠️ SOME MISSING'}`);

    return {
      success: true,
      actionResults: actionResults,
      allIconsPresent: allIconsPresent
    };

  } catch (error) {
    console.error('💥 Action icons test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test Updated At column
 */
export const testUpdatedAtColumn = () => {
  try {
    console.log('📅 Testing Updated At column...');
    console.log('');

    // Check for Updated column header
    const headers = document.querySelectorAll('th');
    let updatedHeader = null;
    
    headers.forEach((header, index) => {
      console.log(`Header ${index + 1}: "${header.textContent}"`);
      if (header.textContent.includes('Updated')) {
        updatedHeader = header;
      }
    });

    console.log('');
    console.log(`📋 Updated column header: ${updatedHeader ? '✅ FOUND' : '❌ MISSING'}`);

    if (!updatedHeader) {
      return {
        success: false,
        message: 'Updated At column header not found'
      };
    }

    // Check for updated date data in table rows
    const tableRows = document.querySelectorAll('tbody tr');
    let updatedDateCells = 0;
    let validDates = 0;
    let naDates = 0;

    tableRows.forEach((row, index) => {
      const cells = row.querySelectorAll('td');
      // Assuming Updated is the 6th column (index 5)
      const updatedCell = cells[5];
      
      if (updatedCell) {
        updatedDateCells++;
        const text = updatedCell.textContent.trim();
        
        if (text === 'N/A') {
          naDates++;
        } else if (text.includes('/') && text.length >= 8) {
          validDates++;
        }
        
        console.log(`Row ${index + 1} Updated: "${text}"`);
      }
    });

    console.log('');
    console.log('📅 Updated Date Analysis:');
    console.log(`   Total rows: ${tableRows.length}`);
    console.log(`   Updated cells: ${updatedDateCells}`);
    console.log(`   Valid dates: ${validDates}`);
    console.log(`   N/A dates: ${naDates}`);

    const columnWorking = updatedDateCells === tableRows.length;

    console.log('');
    console.log(`📅 Updated Column Result: ${columnWorking ? '✅ WORKING' : '❌ ISSUES FOUND'}`);

    return {
      success: true,
      updatedColumn: {
        headerFound: !!updatedHeader,
        totalRows: tableRows.length,
        updatedCells: updatedDateCells,
        validDates: validDates,
        naDates: naDates,
        columnWorking: columnWorking
      }
    };

  } catch (error) {
    console.error('💥 Updated At column test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Test clickable statistics filtering
 */
export const testClickableFiltering = () => {
  try {
    console.log('🖱️ Testing clickable statistics filtering...');
    console.log('');

    const statsCards = document.querySelectorAll('.grid .cursor-pointer');
    
    if (statsCards.length < 3) {
      console.log('❌ Not enough statistics cards found');
      return { success: false };
    }

    // Get initial row count
    const getRowCount = () => document.querySelectorAll('tbody tr').length;
    const initialRows = getRowCount();
    
    console.log(`📊 Initial table rows: ${initialRows}`);

    // Test clicking Active card
    console.log('');
    console.log('1️⃣ Testing Active filter...');
    const activeCard = statsCards[1]; // Second card should be Active
    activeCard.click();

    setTimeout(() => {
      const activeRows = getRowCount();
      console.log(`   Rows after clicking Active: ${activeRows}`);
      
      // Test clicking Inactive card
      setTimeout(() => {
        console.log('');
        console.log('2️⃣ Testing Inactive filter...');
        const inactiveCard = statsCards[2]; // Third card should be Inactive
        inactiveCard.click();

        setTimeout(() => {
          const inactiveRows = getRowCount();
          console.log(`   Rows after clicking Inactive: ${inactiveRows}`);
          
          // Test clicking Total card to reset
          setTimeout(() => {
            console.log('');
            console.log('3️⃣ Testing Total (reset) filter...');
            const totalCard = statsCards[0]; // First card should be Total
            totalCard.click();

            setTimeout(() => {
              const totalRows = getRowCount();
              console.log(`   Rows after clicking Total: ${totalRows}`);
              
              console.log('');
              console.log('🎯 Filtering Test Results:');
              console.log(`   Initial rows: ${initialRows}`);
              console.log(`   Active filtered: ${activeRows}`);
              console.log(`   Inactive filtered: ${inactiveRows}`);
              console.log(`   Reset to total: ${totalRows}`);
              
              const filteringWorks = activeRows < initialRows && 
                                   inactiveRows < initialRows && 
                                   totalRows === initialRows;
              
              console.log(`   Filtering works: ${filteringWorks ? '✅' : '❌'}`);
            }, 200);
          }, 200);
        }, 200);
      }, 200);
    }, 200);

    return {
      success: true,
      message: 'Clickable filtering test initiated'
    };

  } catch (error) {
    console.error('💥 Clickable filtering test failed:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Run all Services page tests
 */
export const runAllServicesTests = () => {
  console.log('🚀 RUNNING ALL SERVICES PAGE TESTS...');
  console.log('');

  // Test 1: Statistics cards
  console.log('📊 Testing Statistics Cards...');
  const statsTest = testServicesStatistics();
  console.log('');

  // Test 2: Action icons
  console.log('🔧 Testing Action Icons...');
  const actionsTest = testActionIcons();
  console.log('');

  // Test 3: Updated At column
  console.log('📅 Testing Updated At Column...');
  const updatedTest = testUpdatedAtColumn();
  console.log('');

  // Test 4: Clickable filtering
  console.log('🖱️ Testing Clickable Filtering...');
  const filterTest = testClickableFiltering();
  console.log('');

  // Summary
  setTimeout(() => {
    console.log('📋 SERVICES PAGE TEST SUMMARY:');
    console.log('');
    console.log('1️⃣ Statistics Cards:');
    console.log(`   Status: ${statsTest.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (statsTest.stats) {
      console.log(`   - Total: ${statsTest.stats.total}`);
      console.log(`   - Active: ${statsTest.stats.active}`);
      console.log(`   - Inactive: ${statsTest.stats.inactive}`);
      console.log(`   - Math correct: ${statsTest.mathCorrect ? '✅' : '❌'}`);
    }

    console.log('');
    console.log('2️⃣ Action Icons:');
    console.log(`   Status: ${actionsTest.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (actionsTest.actionResults) {
      console.log(`   - All icons present: ${actionsTest.allIconsPresent ? '✅' : '❌'}`);
      console.log(`   - View icons: ${actionsTest.actionResults.viewIcons}`);
      console.log(`   - Edit icons: ${actionsTest.actionResults.editIcons}`);
      console.log(`   - Toggle icons: ${actionsTest.actionResults.toggleIcons}`);
      console.log(`   - Delete icons: ${actionsTest.actionResults.deleteIcons}`);
    }

    console.log('');
    console.log('3️⃣ Updated At Column:');
    console.log(`   Status: ${updatedTest.success ? '✅ PASSED' : '❌ FAILED'}`);
    if (updatedTest.updatedColumn) {
      console.log(`   - Header found: ${updatedTest.updatedColumn.headerFound ? '✅' : '❌'}`);
      console.log(`   - Column working: ${updatedTest.updatedColumn.columnWorking ? '✅' : '❌'}`);
      console.log(`   - Valid dates: ${updatedTest.updatedColumn.validDates}`);
      console.log(`   - N/A dates: ${updatedTest.updatedColumn.naDates}`);
    }

    console.log('');
    console.log('4️⃣ Clickable Filtering:');
    console.log(`   Status: ${filterTest.success ? '✅ INITIATED' : '❌ FAILED'}`);

    console.log('');
    console.log('🎉 ALL SERVICES PAGE TESTS COMPLETED!');
  }, 2000);

  return {
    statistics: statsTest,
    actions: actionsTest,
    updatedColumn: updatedTest,
    filtering: filterTest
  };
};

// Make functions available globally
if (typeof window !== 'undefined') {
  window.testServicesStatistics = testServicesStatistics;
  window.testActionIcons = testActionIcons;
  window.testUpdatedAtColumn = testUpdatedAtColumn;
  window.testClickableFiltering = testClickableFiltering;
  window.runAllServicesTests = runAllServicesTests;
  
  console.log('🔧 Services Page Test Functions Available:');
  console.log('- window.testServicesStatistics() - Test statistics cards');
  console.log('- window.testActionIcons() - Test action icons');
  console.log('- window.testUpdatedAtColumn() - Test updated at column');
  console.log('- window.testClickableFiltering() - Test clickable filtering');
  console.log('- window.runAllServicesTests() - Run all tests');
}
