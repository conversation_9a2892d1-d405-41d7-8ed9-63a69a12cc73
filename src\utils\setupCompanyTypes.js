import { sql } from '../config/database.js';

/**
 * Setup company types table and seed initial data
 */
export const setupCompanyTypes = async () => {
  try {
    console.log('🔄 Setting up company types table...');

    // Create company_types table
    await sql`
      CREATE TABLE IF NOT EXISTS company_types (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        status VARCHAR(50) DEFAULT 'Active',
        "isActive" BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    console.log('✅ Company types table created successfully');

    // Check if we already have data
    const existingData = await sql`SELECT COUNT(*) as count FROM company_types`;
    const count = parseInt(existingData[0].count);

    if (count === 0) {
      console.log('🌱 Seeding initial company types data...');
      
      // Insert initial company types
      const initialCompanyTypes = [
        {
          name: 'Private Limited Company',
          description: 'A private company limited by shares, commonly used for small to medium businesses'
        },
        {
          name: 'Public Limited Company',
          description: 'A public company whose shares can be traded on stock exchanges'
        },
        {
          name: 'Limited Liability Partnership (LLP)',
          description: 'A partnership where partners have limited liability for the debts of the business'
        },
        {
          name: 'Partnership Firm',
          description: 'A business structure where two or more individuals share ownership'
        },
        {
          name: 'Sole Proprietorship',
          description: 'A business owned and operated by a single individual'
        },
        {
          name: 'One Person Company (OPC)',
          description: 'A company incorporated with only one person as a member'
        },
        {
          name: 'Section 8 Company',
          description: 'A non-profit company formed for promoting commerce, art, science, sports, education, research, social welfare, religion, charity, protection of environment'
        },
        {
          name: 'Producer Company',
          description: 'A company formed by primary producers for activities related to primary produce'
        },
        {
          name: 'Startup',
          description: 'An innovative business entity recognized under the Startup India initiative'
        },
        {
          name: 'MSME',
          description: 'Micro, Small and Medium Enterprise as classified under MSME Act'
        }
      ];

      for (const companyType of initialCompanyTypes) {
        await sql`
          INSERT INTO company_types (name, description, status, "isActive", created_at, updated_at)
          VALUES (
            ${companyType.name},
            ${companyType.description},
            'Active',
            true,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
          )
        `;
      }

      console.log(`✅ Seeded ${initialCompanyTypes.length} company types successfully`);
    } else {
      console.log(`ℹ️ Company types table already has ${count} records, skipping seeding`);
    }

    // Verify the setup
    const finalCount = await sql`SELECT COUNT(*) as count FROM company_types`;
    console.log(`📊 Total company types in database: ${finalCount[0].count}`);

    return {
      success: true,
      message: 'Company types setup completed successfully',
      count: parseInt(finalCount[0].count)
    };

  } catch (error) {
    console.error('❌ Error setting up company types:', error);
    throw error;
  }
};

/**
 * Add company types table to existing database
 */
export const addCompanyTypesTable = async () => {
  try {
    console.log('🔄 Adding company types table to existing database...');

    // Check if table already exists
    const tableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'company_types'
      )
    `;

    if (tableExists[0].exists) {
      console.log('ℹ️ Company types table already exists');
      return { success: true, message: 'Table already exists' };
    }

    // Create the table
    await sql`
      CREATE TABLE company_types (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        status VARCHAR(50) DEFAULT 'Active',
        "isActive" BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    console.log('✅ Company types table created successfully');

    // Seed initial data
    await setupCompanyTypes();

    return {
      success: true,
      message: 'Company types table added and seeded successfully'
    };

  } catch (error) {
    console.error('❌ Error adding company types table:', error);
    throw error;
  }
};

// Make functions available globally for testing
if (typeof window !== 'undefined') {
  window.setupCompanyTypes = setupCompanyTypes;
  window.addCompanyTypesTable = addCompanyTypesTable;
}
