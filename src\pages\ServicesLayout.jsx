import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { CogIcon, TagIcon } from '@heroicons/react/24/outline';
import Services from './Services';
import ServiceCategories from './ServiceCategories';

const ServicesLayout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Determine active tab from URL
  const getActiveTab = () => {
    if (location.pathname === '/services' && !location.pathname.includes('/services/categories')) {
      return 'services';
    }
    return 'categories'; // Default to categories first
  };

  const [activeTab, setActiveTab] = useState(getActiveTab());

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    if (tab === 'services') {
      navigate('/services');
    } else {
      navigate('/services/categories');
    }
  };

  const tabs = [
    {
      id: 'categories',
      name: 'Categories',
      icon: TagIcon,
      description: 'Manage service categories'
    },
    {
      id: 'services',
      name: 'Services',
      icon: CogIcon,
      description: 'Manage service offerings'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">Services Management</h1>
        <p className="mt-2 text-sm text-gray-700">
          Manage services and their categories for vendor offerings
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon
                  className={`-ml-0.5 mr-2 h-5 w-5 ${
                    activeTab === tab.id
                      ? 'text-blue-500'
                      : 'text-gray-400 group-hover:text-gray-500'
                  }`}
                />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'categories' && <ServiceCategories />}
        {activeTab === 'services' && <Services />}
      </div>
    </div>
  );
};

export default ServicesLayout;
