import { useState, useRef, useEffect } from 'react';
import { CloudArrowUpIcon, DocumentIcon, PhotoIcon, XMarkIcon, EyeIcon } from '@heroicons/react/24/outline';
import { uploadFileToS3, uploadMultipleFiles, deleteFileFromS3, getPublicFileUrl } from '../../services/s3Service';

const S3FileUpload = ({
  module,
  recordId,
  fileType,
  allowedTypes = 'all',
  multiple = false,
  maxFiles = 5,
  onFilesUploaded,
  onFileDeleted,
  existingFiles = [],
  label = 'Upload Files',
  description = 'Drag and drop files here, or click to select files'
}) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [dragOver, setDragOver] = useState(false);
  const [files, setFiles] = useState(existingFiles);

  // Update files when existingFiles prop changes
  useEffect(() => {
    setFiles(existingFiles);
  }, [existingFiles]);
  const fileInputRef = useRef(null);

  const handleFileSelect = (selectedFiles) => {
    const fileArray = Array.from(selectedFiles);
    
    if (!multiple && fileArray.length > 1) {
      alert('Only one file is allowed');
      return;
    }
    
    if (multiple && fileArray.length > maxFiles) {
      alert(`Maximum ${maxFiles} files allowed`);
      return;
    }
    
    uploadFiles(fileArray);
  };

  const uploadFiles = async (filesToUpload) => {
    console.log('🔍 S3FileUpload - uploadFiles called with:', {
      recordId,
      module,
      fileType,
      fileCount: filesToUpload.length,
      files: filesToUpload.map(f => ({ name: f.name, size: f.size, type: f.type }))
    });

    if (!recordId) {
      console.error('❌ Record ID is missing!');
      alert('Record ID is required for file upload');
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      let results;

      // TEMPORARY FIX: Just use S3 uploads for now, skip database integration
      console.log('🚀 Starting S3 upload for:', { module, recordId, fileType, fileCount: filesToUpload.length });

      if (multiple) {
        results = await uploadMultipleFiles(
          filesToUpload,
          module,
          recordId,
          fileType,
          setUploadProgress
        );
      } else {
        const result = await uploadFileToS3(
          filesToUpload[0],
          module,
          recordId,
          fileType,
          setUploadProgress
        );
        results = [{ success: true, file: result }];
      }

      // Process results
      const successfulUploads = results.filter(r => r.success).map(r => r.file);
      const failedUploads = results.filter(r => !r.success);

      if (successfulUploads.length > 0) {
        const updatedFiles = multiple ? [...files, ...successfulUploads] : successfulUploads;
        setFiles(updatedFiles);

        if (onFilesUploaded) {
          onFilesUploaded(updatedFiles);
        }
      }

      if (failedUploads.length > 0) {
        const errorMessages = failedUploads.map(f => `${f.fileName}: ${f.error}`).join('\n');
        alert(`Some files failed to upload:\n${errorMessages}`);
      }

      if (successfulUploads.length > 0) {
        alert(`Successfully uploaded ${successfulUploads.length} file(s)`);
      }

    } catch (error) {
      console.error('Upload error:', error);
      alert(`Upload failed: ${error.message}`);
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const handleFileDelete = async (fileToDelete) => {
    if (!confirm('Are you sure you want to delete this file?')) {
      return;
    }

    try {
      // TEMPORARY FIX: Just delete from S3 for now
      console.log('🗑️ Deleting file from S3:', fileToDelete.key);
      await deleteFileFromS3(fileToDelete.key);

      const updatedFiles = files.filter(f => f.key !== fileToDelete.key);
      setFiles(updatedFiles);

      if (onFileDeleted) {
        onFileDeleted(fileToDelete, updatedFiles);
      }

      alert('File deleted successfully');
    } catch (error) {
      console.error('Delete error:', error);
      alert(`Failed to delete file: ${error.message}`);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    const droppedFiles = e.dataTransfer.files;
    handleFileSelect(droppedFiles);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType) => {
    if (fileType && fileType.startsWith('image/')) {
      return <PhotoIcon className="h-8 w-8 text-blue-500" />;
    }
    return <DocumentIcon className="h-8 w-8 text-gray-500" />;
  };

  const openFilePreview = (file) => {
    const url = getPublicFileUrl(file.key);
    window.open(url, '_blank');
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          {label}
        </label>
        
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            dragOver
              ? 'border-primary-500 bg-primary-50'
              : uploading
              ? 'border-gray-300 bg-gray-50'
              : 'border-gray-300 hover:border-primary-400 hover:bg-gray-50'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {uploading ? (
            <div className="space-y-3">
              <CloudArrowUpIcon className="h-12 w-12 text-primary-500 mx-auto animate-bounce" />
              <div>
                <p className="text-sm text-gray-600">Uploading to S3...</p>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div
                    className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">{uploadProgress}%</p>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <CloudArrowUpIcon className="h-12 w-12 text-gray-400 mx-auto" />
              <div>
                <p className="text-sm text-gray-600">{description}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {multiple ? `Up to ${maxFiles} files` : 'Single file only'} • Max 10MB per file • Saved to AWS S3
                </p>
              </div>
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="btn-secondary text-sm"
                disabled={uploading}
              >
                Choose Files
              </button>
            </div>
          )}
        </div>

        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
          accept={allowedTypes === 'images' ? 'image/*' : allowedTypes === 'documents' ? '.pdf,.doc,.docx' : '*'}
        />
      </div>

      {/* Uploaded Files List */}
      {files.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">
            Uploaded Files ({files.length}) - Stored in AWS S3
          </h4>
          <div className="space-y-2">
            {files.map((file, index) => (
              <div
                key={file.key || index}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
              >
                <div className="flex items-center space-x-3">
                  {getFileIcon(file.type)}
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {file.originalName || file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(file.size)} • {new Date(file.uploadedAt || Date.now()).toLocaleDateString()}
                    </p>
                    <p className="text-xs text-blue-600">
                      📁 S3: {file.key}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => openFilePreview(file)}
                    className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded"
                    title="View File"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleFileDelete(file)}
                    className="p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded"
                    title="Delete File"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default S3FileUpload;
