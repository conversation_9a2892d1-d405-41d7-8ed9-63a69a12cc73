import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { PlusIcon, PencilIcon, TrashIcon, CheckCircleIcon, XCircleIcon, EyeIcon } from '@heroicons/react/24/outline';
import DataTable from '../components/DataTable/DataTable';

const TypeOfWork = () => {
  const navigate = useNavigate();
  const [showAddForm, setShowAddForm] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [typeOfWorkData, setTypeOfWorkData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [editingId, setEditingId] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all'); // 'all', 'active', 'inactive'

  // Filter data based on status
  const getFilteredData = () => {
    switch (statusFilter) {
      case 'active':
        return typeOfWorkData.filter(item => item.status === 'Active');
      case 'inactive':
        return typeOfWorkData.filter(item => item.status === 'Inactive');
      default:
        return typeOfWorkData;
    }
  };

  // Transform database data to match expected format
  const transformTypeOfWorkData = (data) => {
    return data.map(item => ({
      ...item,
      status: item.isActive !== undefined ? (item.isActive ? 'Active' : 'Inactive') : (item.status || 'Active'),
      createdDate: item.createdAt ? new Date(item.createdAt).toLocaleDateString() : (item.createdDate || new Date().toLocaleDateString()),
      updatedDate: item.updatedAt ? new Date(item.updatedAt).toLocaleDateString() : (item.updatedDate || null)
    }));
  };

  // Load type of work from database
  const loadTypeOfWork = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading type of work from database...');
      // Import and use database service
      const { getAllTypeOfWork } = await import('../services/typeOfWorkService');
      const dbTypeOfWork = await getAllTypeOfWork();
      console.log('✅ Type of work loaded:', dbTypeOfWork);

      setTypeOfWorkData(transformTypeOfWorkData(dbTypeOfWork || []));
    } catch (err) {
      console.error('❌ Error loading type of work:', err);
      setTypeOfWorkData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTypeOfWork();
  }, []);



  // Toggle status function
  const toggleStatus = async (id, currentStatus) => {
    try {
      const newStatus = currentStatus === 'Active' ? 'Inactive' : 'Active';
      console.log(`🔄 Toggling status for ${id} from ${currentStatus} to ${newStatus}`);

      // Update in database using the status update function
      const { updateTypeOfWorkStatus } = await import('../services/typeOfWorkService');
      const result = await updateTypeOfWorkStatus(id, newStatus);
      console.log(`✅ Status updated successfully:`, result);

      // Reload the entire data to ensure consistency
      await loadTypeOfWork();
      alert(`✅ Status updated to ${newStatus} successfully!`);
    } catch (error) {
      console.error('❌ Error updating status:', error);
      alert(`❌ Error updating status: ${error.message || 'Please try again.'}`);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation
    if (!formData.name || formData.name.trim() === '') {
      alert('❌ Work Type Name is required');
      return;
    }

    if (!formData.description || formData.description.trim() === '') {
      alert('❌ Description is required');
      return;
    }

    if (formData.name.trim().length < 3) {
      alert('❌ Work Type Name must be at least 3 characters long');
      return;
    }

    if (formData.description.trim().length < 10) {
      alert('❌ Description must be at least 10 characters long');
      return;
    }

    try {
      setLoading(true);
      console.log('🚀 Submitting type of work form...');
      console.log('📋 Form data:', formData);

      if (editingId) {
        // Update existing type of work
        console.log(`✏️ Updating type of work with ID: ${editingId}`);
        const { updateTypeOfWork } = await import('../services/typeOfWorkService');
        const result = await updateTypeOfWork(editingId, formData);
        console.log('✅ Type of work updated successfully:', result);
        alert('✅ Type of work updated successfully!');
      } else {
        // Create new type of work
        console.log('🆕 Creating new type of work');
        const { createTypeOfWork } = await import('../services/typeOfWorkService');
        const result = await createTypeOfWork(formData);
        console.log('✅ Type of work created successfully:', result);
        alert('✅ Type of work created successfully!');
      }

      // Reset form and reload data
      setFormData({ name: '', description: '' });
      setShowAddForm(false);
      setEditingId(null);
      await loadTypeOfWork();
    } catch (error) {
      console.error('❌ Error saving type of work:', error);
      alert(`❌ Error saving type of work: ${error.message || 'Please try again.'}`);
    } finally {
      setLoading(false);
    }
  };



  // Handle delete
  const handleDelete = async (id, name) => {
    const confirmMessage = `Are you sure you want to delete "${name}"?\n\nThis action cannot be undone and will permanently remove this type of work from the system.`;

    if (confirm(confirmMessage)) {
      try {
        console.log(`🗑️ Deleting type of work: ${name} (ID: ${id})`);
        const { deleteTypeOfWork } = await import('../services/typeOfWorkService');
        await deleteTypeOfWork(id);
        console.log('✅ Type of work deleted successfully');

        await loadTypeOfWork();
        alert(`✅ "${name}" has been deleted successfully!`);
      } catch (error) {
        console.error('❌ Error deleting type of work:', error);
        alert(`❌ Error deleting type of work: ${error.message || 'Please try again.'}`);
      }
    }
  };

  // Handle edit
  const handleEdit = (item) => {
    setFormData({
      name: item.name,
      description: item.description
    });
    setEditingId(item.id);
    setShowAddForm(true);
  };

  const columns = [
    {
      key: 'name',
      label: 'Work Type',
      sortable: true,
      render: (value) => (
        <div className="font-medium text-gray-900">{value}</div>
      )
    },
    {
      key: 'description',
      label: 'Description',
      sortable: false,
      render: (value) => (
        <div className="max-w-md text-gray-600 truncate" title={value}>
          {value}
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'Active'
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'createdDate',
      label: 'Created Date',
      sortable: true,
      render: (value) => (
        <span className="text-gray-600">{value}</span>
      )
    },
    {
      key: 'updatedDate',
      label: 'Updated Date',
      sortable: true,
      render: (value) => (
        <span className="text-gray-600">{value || 'N/A'}</span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      filterable: false,
      render: (_, row) => (
        <div className="flex space-x-2">
          <button
            onClick={() => {
              setFormData({
                name: row.name,
                description: row.description
              });
              setEditingId(row.id);
              setShowViewModal(true);
            }}
            className="text-gray-600 hover:text-gray-800"
            title="View"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleEdit(row)}
            className="text-blue-600 hover:text-blue-800"
            title="Edit"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => toggleStatus(row.id, row.status)}
            className={`${
              row.status === 'Active'
                ? 'text-green-600 hover:text-green-800'
                : 'text-gray-400 hover:text-gray-600'
            }`}
            title={`${row.status} - Click to toggle to ${row.status === 'Active' ? 'Inactive' : 'Active'}`}
          >
            {row.status === 'Active' ? (
              <CheckCircleIcon className="h-4 w-4" />
            ) : (
              <XCircleIcon className="h-4 w-4" />
            )}
          </button>
          <button
            onClick={() => handleDelete(row.id, row.name)}
            className="text-red-600 hover:text-red-800"
            title="Delete"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ];

  // Action Icons for DataTable Header
  const actionIcons = (
    <>
      <div className="flex flex-col items-center space-y-1">
        <EyeIcon className="w-4 h-4 text-gray-600" />
        <span className="text-xs text-gray-600">View</span>
      </div>
      <div className="flex flex-col items-center space-y-1">
        <PencilIcon className="w-4 h-4 text-blue-600" />
        <span className="text-xs text-blue-600">Edit</span>
      </div>
      <div className="flex flex-col items-center space-y-1">
        <CheckCircleIcon className="w-4 h-4 text-green-600" />
        <span className="text-xs text-green-600">Toggle Active</span>
      </div>
      <div className="flex flex-col items-center space-y-1">
        <XCircleIcon className="w-4 h-4 text-gray-400" />
        <span className="text-xs text-gray-400">Toggle Inactive</span>
      </div>
      <div className="flex flex-col items-center space-y-1">
        <TrashIcon className="w-4 h-4 text-red-600" />
        <span className="text-xs text-red-600">Delete</span>
      </div>
    </>
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center border-b border-gray-200 pb-4 space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Type of Work</h1>
          <p className="mt-1 sm:mt-2 text-gray-600 text-sm sm:text-base">Manage different types of work and services</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-primary flex items-center justify-center w-full sm:w-auto"
          >
            <PlusIcon className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
            <span className="text-sm sm:text-base">Add New Work Type</span>
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Total Types */}
        <div
          onClick={() => setStatusFilter('all')}
          className={`bg-white rounded-lg shadow-sm border p-4 sm:p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            statusFilter === 'all'
              ? 'border-blue-500 ring-2 ring-blue-200 bg-blue-50'
              : 'border-gray-200 hover:border-blue-300'
          }`}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-lg flex items-center justify-center ${
                statusFilter === 'all' ? 'bg-blue-200' : 'bg-blue-100'
              }`}>
                <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
            </div>
            <div className="ml-3 sm:ml-4">
              <p className="text-xs sm:text-sm font-medium text-gray-600">Total Types</p>
              <p className={`text-xl sm:text-2xl font-bold ${
                statusFilter === 'all' ? 'text-blue-700' : 'text-gray-900'
              }`}>{typeOfWorkData.length}</p>
            </div>
          </div>
          {statusFilter === 'all' && (
            <div className="mt-2 text-xs text-blue-600 font-medium">
              ✓ Showing all types
            </div>
          )}
        </div>

        {/* Active Types */}
        <div
          onClick={() => setStatusFilter('active')}
          className={`bg-white rounded-lg shadow-sm border p-4 sm:p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            statusFilter === 'active'
              ? 'border-green-500 ring-2 ring-green-200 bg-green-50'
              : 'border-gray-200 hover:border-green-300'
          }`}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-lg flex items-center justify-center ${
                statusFilter === 'active' ? 'bg-green-200' : 'bg-green-100'
              }`}>
                <svg className="w-4 h-4 sm:w-5 sm:h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <div className="ml-3 sm:ml-4">
              <p className="text-xs sm:text-sm font-medium text-gray-600">Active Types</p>
              <p className={`text-xl sm:text-2xl font-bold ${
                statusFilter === 'active' ? 'text-green-700' : 'text-green-600'
              }`}>
                {typeOfWorkData.filter(item => item.status === 'Active').length}
              </p>
            </div>
          </div>
          {statusFilter === 'active' && (
            <div className="mt-2 text-xs text-green-600 font-medium">
              ✓ Showing active types only
            </div>
          )}
        </div>

        {/* Inactive Types */}
        <div
          onClick={() => setStatusFilter('inactive')}
          className={`bg-white rounded-lg shadow-sm border p-4 sm:p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            statusFilter === 'inactive'
              ? 'border-red-500 ring-2 ring-red-200 bg-red-50'
              : 'border-gray-200 hover:border-red-300'
          }`}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-lg flex items-center justify-center ${
                statusFilter === 'inactive' ? 'bg-red-200' : 'bg-red-100'
              }`}>
                <svg className="w-4 h-4 sm:w-5 sm:h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
            </div>
            <div className="ml-3 sm:ml-4">
              <p className="text-xs sm:text-sm font-medium text-gray-600">Inactive Types</p>
              <p className={`text-xl sm:text-2xl font-bold ${
                statusFilter === 'inactive' ? 'text-red-700' : 'text-red-600'
              }`}>
                {typeOfWorkData.filter(item => item.status === 'Inactive').length}
              </p>
            </div>
          </div>
          {statusFilter === 'inactive' && (
            <div className="mt-2 text-xs text-red-600 font-medium">
              ✓ Showing inactive types only
            </div>
          )}
        </div>
      </div>

      {/* Filter Status Indicator */}
      {statusFilter !== 'all' && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between bg-blue-50 border border-blue-200 rounded-lg p-3 sm:p-4 space-y-2 sm:space-y-0">
          <div className="flex items-center">
            <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
            </svg>
            <span className="text-xs sm:text-sm font-medium text-blue-800">
              Showing {statusFilter} types only ({getFilteredData().length} of {typeOfWorkData.length} total)
            </span>
          </div>
          <button
            onClick={() => setStatusFilter('all')}
            className="text-xs sm:text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center justify-center sm:justify-start"
          >
            <svg className="w-3 h-3 sm:w-4 sm:h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            Clear Filter
          </button>
        </div>
      )}

      {/* Data Table */}
      <div className="card">
        <DataTable
          data={getFilteredData()}
          columns={columns}
          title="Type of Work"
          defaultPageSize={10}
          enableExport={true}
          enableColumnToggle={true}
          enableFiltering={true}
          enableSorting={true}
          loading={loading}
          actionIcons={actionIcons}
        />
      </div>

      {/* Add/Edit Form Modal */}
      {showAddForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => {
              setShowAddForm(false);
              setEditingId(null);
              setFormData({ name: '', description: '' });
            }}></div>
            <div className="relative bg-white rounded-lg max-w-md w-full p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">
                  {editingId ? 'Edit Work Type' : 'Add New Work Type'}
                </h3>
                <button
                  onClick={() => {
                    setShowAddForm(false);
                    setEditingId(null);
                    setFormData({ name: '', description: '' });
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Work Type Name */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Work Type Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter work type name"
                    required
                  />
                </div>

                {/* Description */}
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter description"
                    required
                  />
                </div>

                {/* Created Date Info */}
                <div className="text-sm text-gray-500">
                  <p>Created Date: {new Date().toLocaleDateString()} (Auto-generated)</p>
                  <p>Status: Active (Default)</p>
                </div>

                {/* Form Actions */}
                <div className="flex space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddForm(false);
                      setEditingId(null);
                      setFormData({ name: '', description: '' });
                    }}
                    className="flex-1 btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="flex-1 btn-primary disabled:opacity-50"
                  >
                    {loading ? 'Saving...' : (editingId ? 'Update' : 'Save')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* View Modal */}
      {showViewModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => {
              setShowViewModal(false);
              setEditingId(null);
              setFormData({ name: '', description: '' });
            }}></div>
            <div className="relative bg-white rounded-lg max-w-md w-full p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">
                  View Work Type
                </h3>
                <button
                  onClick={() => {
                    setShowViewModal(false);
                    setEditingId(null);
                    setFormData({ name: '', description: '' });
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-4">
                {/* Work Type Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Work Type Name
                  </label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                    {formData.name}
                  </div>
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 min-h-[80px]">
                    {formData.description}
                  </div>
                </div>

                {/* Status and Date Info */}
                <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
                  <p><strong>Status:</strong> {typeOfWorkData.find(item => item.id === editingId)?.status || 'Active'}</p>
                  <p><strong>Created Date:</strong> {typeOfWorkData.find(item => item.id === editingId)?.createdDate || 'N/A'}</p>
                </div>

                {/* Modal Actions */}
                <div className="flex space-x-3 pt-6">
                  <button
                    type="button"
                    onClick={() => {
                      setShowViewModal(false);
                      setEditingId(null);
                      setFormData({ name: '', description: '' });
                    }}
                    className="flex-1 btn-secondary"
                  >
                    Close
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowViewModal(false);
                      setShowAddForm(true);
                    }}
                    className="flex-1 btn-primary"
                  >
                    Edit
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TypeOfWork;
