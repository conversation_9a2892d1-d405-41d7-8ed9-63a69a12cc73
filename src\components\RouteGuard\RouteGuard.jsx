import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { isAuthenticated } from '../../services/enhancedAuthService';

const RouteGuard = ({ children }) => {
  const location = useLocation();

  useEffect(() => {
    // If on login page but already authenticated, redirect to dashboard
    if (location.pathname === '/login' && isAuthenticated()) {
      console.log('🔐 RouteGuard: Already authenticated, redirecting to dashboard');
      window.location.replace('/dashboard');
    }
  }, [location.pathname]);

  return children;
};

export default RouteGuard;
