import { useState, useEffect } from 'react';
import { PlusIcon, PencilIcon, TrashIcon, XMarkIcon, EyeIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import DataTable from '../components/DataTable/DataTable';
import { getAllServices, createService, updateService, deleteService, hardDeleteService } from '../services/servicesService';
import { getActiveServiceCategories } from '../services/serviceCategoriesService';

const Services = () => {
  const [services, setServices] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingService, setEditingService] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    isActive: true
  });
  const [validationErrors, setValidationErrors] = useState({});
  const [statusFilter, setStatusFilter] = useState('all'); // 'all', 'active', 'inactive'
  const [showViewModal, setShowViewModal] = useState(false);
  const [viewingService, setViewingService] = useState(null);

  // Filter services based on status
  const getFilteredServices = () => {
    switch (statusFilter) {
      case 'active':
        return services.filter(service => service.isActive);
      case 'inactive':
        return services.filter(service => !service.isActive);
      default:
        return services;
    }
  };

  // Load services and categories on component mount
  useEffect(() => {
    loadServices();
    loadCategories();
  }, []);

  const loadServices = async () => {
    try {
      setLoading(true);
      const servicesData = await getAllServices(true); // Include inactive services
      setServices(servicesData);
    } catch (error) {
      console.error('Error loading services:', error);
      alert('Failed to load services');
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const categoriesData = await getActiveServiceCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading service categories:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear validation error for this field
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  const validateForm = () => {
    const errors = {};
    
    if (!formData.name.trim()) {
      errors.name = 'Service name is required';
    }
    
    if (formData.name.trim().length < 2) {
      errors.name = 'Service name must be at least 2 characters';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      
      if (editingService) {
        await updateService(editingService.id, formData);
        alert('Service updated successfully!');
      } else {
        await createService(formData);
        alert('Service created successfully!');
      }
      
      await loadServices();
      resetForm();
    } catch (error) {
      console.error('Error saving service:', error);
      alert(error.message || 'Failed to save service');
    } finally {
      setLoading(false);
    }
  };

  const handleView = (service) => {
    setViewingService(service);
    setShowViewModal(true);
  };

  const handleEdit = (service) => {
    setEditingService(service);
    setFormData({
      name: service.name,
      description: service.description || '',
      category: service.category || '',
      isActive: service.isActive
    });
    setShowEditForm(true);
  };

  const handleToggleStatus = async (service) => {
    const newStatus = !service.isActive;
    const action = newStatus ? 'activate' : 'deactivate';

    if (window.confirm(`Are you sure you want to ${action} "${service.name}"?`)) {
      try {
        await updateService(service.id, { ...service, isActive: newStatus });
        await loadServices();
        alert(`✅ Service ${action}d successfully!`);
      } catch (error) {
        console.error(`Error ${action}ing service:`, error);
        alert(`❌ Failed to ${action} service. Please try again.`);
      }
    }
  };

  const handleDelete = async (service) => {
    const confirmMessage = service.isActive
      ? `Are you sure you want to deactivate "${service.name}"?\n\nThis will hide it from vendor forms but keep it in the database for existing records.`
      : `Are you sure you want to permanently delete "${service.name}"?\n\nThis action cannot be undone.`;

    if (window.confirm(confirmMessage)) {
      try {
        setLoading(true);
        console.log('🔄 Attempting to delete service:', service.id, service.name);

        let result;
        if (service.isActive) {
          // Soft delete for active services
          result = await deleteService(service.id);
          console.log('✅ Soft delete result:', result);
          alert('Service deactivated successfully!');
        } else {
          // Hard delete for inactive services
          result = await hardDeleteService(service.id);
          console.log('✅ Hard delete result:', result);
          alert('Service permanently deleted!');
        }

        await loadServices();
      } catch (error) {
        console.error('❌ Error deleting service:', error);
        alert(error.message || 'Failed to delete service');
      } finally {
        setLoading(false);
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      category: '',
      isActive: true
    });
    setValidationErrors({});
    setShowAddForm(false);
    setShowEditForm(false);
    setEditingService(null);
  };

  const columns = [
    {
      key: 'name',
      label: 'Service Name',
      sortable: true,
      render: (value, row) => (
        <div className="font-medium text-gray-900">{value}</div>
      )
    },
    {
      key: 'description',
      label: 'Description',
      sortable: false,
      render: (value) => (
        <div className="max-w-md text-gray-600 truncate" title={value}>
          {value}
        </div>
      )
    },
    {
      key: 'category',
      label: 'Category',
      render: (value) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          {value || 'General'}
        </span>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value, row) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          row.isActive 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {row.isActive ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      key: 'createdAt',
      label: 'Created',
      sortable: true,
      render: (value) => (
        <div className="text-sm text-gray-500">
          {new Date(value).toLocaleDateString()}
        </div>
      )
    },
    {
      key: 'updatedAt',
      label: 'Updated',
      sortable: true,
      render: (value) => (
        <div className="text-sm text-gray-500">
          {value ? new Date(value).toLocaleDateString() : 'N/A'}
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      filterable: false,
      render: (_, row) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleView(row)}
            className="text-gray-600 hover:text-gray-900"
            title="View Service"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleEdit(row)}
            className="text-blue-600 hover:text-blue-900"
            title="Edit Service"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleToggleStatus(row)}
            className={row.isActive ? "text-orange-600 hover:text-orange-900" : "text-green-600 hover:text-green-900"}
            title={row.isActive ? "Deactivate Service" : "Activate Service"}
          >
            {row.isActive ? <XCircleIcon className="h-4 w-4" /> : <CheckCircleIcon className="h-4 w-4" />}
          </button>
          <button
            onClick={() => handleDelete(row)}
            className="text-red-600 hover:text-red-900"
            title={row.isActive ? "Deactivate Service" : "Permanently Delete Service"}
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      )
    }
  ];



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-gray-900">Services Management</h1>
          <p className="mt-1 sm:mt-2 text-sm text-gray-700">
            Manage service offerings that vendors can provide
          </p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="btn-primary flex items-center justify-center space-x-2 w-full sm:w-auto"
        >
          <PlusIcon className="h-4 w-4" />
          <span className="text-sm sm:text-base">Add Service</span>
        </button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Total Services */}
        <div
          onClick={() => setStatusFilter('all')}
          className={`bg-white rounded-lg shadow-sm border p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            statusFilter === 'all'
              ? 'border-blue-500 ring-2 ring-blue-200 bg-blue-50'
              : 'border-gray-200 hover:border-blue-300'
          }`}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                statusFilter === 'all' ? 'bg-blue-200' : 'bg-blue-100'
              }`}>
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Services</p>
              <p className={`text-2xl font-bold ${
                statusFilter === 'all' ? 'text-blue-700' : 'text-gray-900'
              }`}>{services.length}</p>
            </div>
          </div>
          {statusFilter === 'all' && (
            <div className="mt-2 text-xs text-blue-600 font-medium">
              ✓ Showing all services
            </div>
          )}
        </div>

        {/* Active Services */}
        <div
          onClick={() => setStatusFilter('active')}
          className={`bg-white rounded-lg shadow-sm border p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            statusFilter === 'active'
              ? 'border-green-500 ring-2 ring-green-200 bg-green-50'
              : 'border-gray-200 hover:border-green-300'
          }`}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                statusFilter === 'active' ? 'bg-green-200' : 'bg-green-100'
              }`}>
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Services</p>
              <p className={`text-2xl font-bold ${
                statusFilter === 'active' ? 'text-green-700' : 'text-green-600'
              }`}>
                {services.filter(service => service.isActive).length}
              </p>
            </div>
          </div>
          {statusFilter === 'active' && (
            <div className="mt-2 text-xs text-green-600 font-medium">
              ✓ Showing active services only
            </div>
          )}
        </div>

        {/* Inactive Services */}
        <div
          onClick={() => setStatusFilter('inactive')}
          className={`bg-white rounded-lg shadow-sm border p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            statusFilter === 'inactive'
              ? 'border-red-500 ring-2 ring-red-200 bg-red-50'
              : 'border-gray-200 hover:border-red-300'
          }`}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                statusFilter === 'inactive' ? 'bg-red-200' : 'bg-red-100'
              }`}>
                <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Inactive Services</p>
              <p className={`text-2xl font-bold ${
                statusFilter === 'inactive' ? 'text-red-700' : 'text-red-600'
              }`}>
                {services.filter(service => !service.isActive).length}
              </p>
            </div>
          </div>
          {statusFilter === 'inactive' && (
            <div className="mt-2 text-xs text-red-600 font-medium">
              ✓ Showing inactive services only
            </div>
          )}
        </div>
      </div>

      {/* Filter Status Indicator */}
      {statusFilter !== 'all' && (
        <div className="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
            </svg>
            <span className="text-sm font-medium text-blue-800">
              Showing {statusFilter} services only ({getFilteredServices().length} of {services.length} total)
            </span>
          </div>
          <button
            onClick={() => setStatusFilter('all')}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            Clear Filter
          </button>
        </div>
      )}



      {/* Services Table */}
      <div className="card">
        <DataTable
          data={getFilteredServices()}
          columns={columns}
          title="Services"
          defaultPageSize={10}
          enableExport={true}
          enableColumnToggle={true}
          enableFiltering={true}
          enableSorting={true}
          loading={loading}
          searchable={true}
          searchPlaceholder="Search services..."
          emptyMessage="No services found"
          actionIcons={
            <>
              <div className="flex flex-col items-center space-y-1">
                <EyeIcon className="h-4 w-4 text-gray-600" />
                <span className="text-xs text-gray-600">View</span>
              </div>

              <div className="flex flex-col items-center space-y-1">
                <PencilIcon className="h-4 w-4 text-blue-600" />
                <span className="text-xs text-blue-600">Edit</span>
              </div>

              <div className="flex flex-col items-center space-y-1">
                <CheckCircleIcon className="h-4 w-4 text-green-600" />
                <span className="text-xs text-green-600">Toggle Active</span>
              </div>

              <div className="flex flex-col items-center space-y-1">
                <XCircleIcon className="h-4 w-4 text-orange-600" />
                <span className="text-xs text-orange-600">Toggle Inactive</span>
              </div>

              <div className="flex flex-col items-center space-y-1">
                <TrashIcon className="h-4 w-4 text-red-600" />
                <span className="text-xs text-red-600">Delete</span>
              </div>
            </>
          }
        />
      </div>

      {/* Add Service Modal */}
      {showAddForm && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={resetForm}></div>
            <div className="relative bg-white rounded-lg max-w-md w-full p-6">
              <form onSubmit={handleSubmit}>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Add New Service</h3>
                  <button
                    type="button"
                    onClick={resetForm}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Service Name *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className={`input-field ${validationErrors.name ? 'border-red-500' : ''}`}
                      placeholder="Enter service name"
                      required
                    />
                    {validationErrors.name && (
                      <p className="text-red-500 text-xs mt-1">{validationErrors.name}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={3}
                      className="input-field"
                      placeholder="Enter service description"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category
                    </label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                      className="input-field"
                    >
                      <option value="">Select category</option>
                      {categories.map(category => (
                        <option key={category.id} value={category.name}>{category.name}</option>
                      ))}
                    </select>
                    {categories.length === 0 && (
                      <p className="text-sm text-gray-500 italic mt-1">
                        No categories available. Please add categories from the Categories tab.
                      </p>
                    )}
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleInputChange}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label className="ml-2 text-sm text-gray-700">
                      Active (available for selection)
                    </label>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                    disabled={loading}
                  >
                    {loading ? 'Creating...' : 'Create Service'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit Service Modal */}
      {showEditForm && editingService && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={resetForm}></div>
            <div className="relative bg-white rounded-lg max-w-md w-full p-6">
              <form onSubmit={handleSubmit}>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Edit Service</h3>
                  <button
                    type="button"
                    onClick={resetForm}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Service Name *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className={`input-field ${validationErrors.name ? 'border-red-500' : ''}`}
                      placeholder="Enter service name"
                      required
                    />
                    {validationErrors.name && (
                      <p className="text-red-500 text-xs mt-1">{validationErrors.name}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={3}
                      className="input-field"
                      placeholder="Enter service description"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category
                    </label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                      className="input-field"
                    >
                      <option value="">Select category</option>
                      {categories.map(category => (
                        <option key={category.id} value={category.name}>{category.name}</option>
                      ))}
                    </select>
                    {categories.length === 0 && (
                      <p className="text-sm text-gray-500 italic mt-1">
                        No categories available. Please add categories from the Categories tab.
                      </p>
                    )}
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleInputChange}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label className="ml-2 text-sm text-gray-700">
                      Active (available for selection)
                    </label>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary"
                    disabled={loading}
                  >
                    {loading ? 'Updating...' : 'Update Service'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* View Service Modal */}
      {showViewModal && viewingService && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => setShowViewModal(false)}></div>
            <div className="relative bg-white rounded-lg max-w-2xl w-full p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">Service Details</h3>
                <button
                  onClick={() => setShowViewModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-6">
                {/* Service Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Service Name</label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    <p className="text-gray-900 font-medium">{viewingService.name}</p>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    <p className="text-gray-900">{viewingService.description || 'No description provided'}</p>
                  </div>
                </div>

                {/* Category */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {viewingService.category || 'General'}
                    </span>
                  </div>
                </div>

                {/* Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      viewingService.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {viewingService.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>

                {/* Timestamps */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Created</label>
                    <div className="p-3 bg-gray-50 rounded-md">
                      <p className="text-gray-900">{new Date(viewingService.createdAt).toLocaleDateString()}</p>
                      <p className="text-xs text-gray-500">{new Date(viewingService.createdAt).toLocaleTimeString()}</p>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Updated</label>
                    <div className="p-3 bg-gray-50 rounded-md">
                      <p className="text-gray-900">
                        {viewingService.updatedAt ? new Date(viewingService.updatedAt).toLocaleDateString() : 'N/A'}
                      </p>
                      {viewingService.updatedAt && (
                        <p className="text-xs text-gray-500">{new Date(viewingService.updatedAt).toLocaleTimeString()}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                <button
                  onClick={() => setShowViewModal(false)}
                  className="btn-secondary"
                >
                  Close
                </button>
                <button
                  onClick={() => {
                    setShowViewModal(false);
                    handleEdit(viewingService);
                  }}
                  className="btn-primary"
                >
                  Edit Service
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Services;
