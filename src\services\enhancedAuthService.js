/**
 * Enhanced Authentication Service
 * Handles username-based authentication for Admin, Sub-Admin, and Vendor users
 */

import { sql } from '../config/database.js';

const AUTH_KEY = 'innoventory_auth';

/**
 * Hash password for comparison (simple implementation)
 */
const hashPassword = async (password) => {
  const encoder = new TextEncoder();
  const data = encoder.encode(password + 'innoventory_salt_2024');
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
};

/**
 * Generate session token
 */
const generateSessionToken = () => {
  return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

/**
 * Enhanced login function - handles username-based authentication
 */
export const login = async (credentials) => {
  try {
    const { username, password } = credentials;

    console.log('🔐 Attempting login for username:', username);

    // Check for default admin first
    if (username === 'admin' && password === 'admin123') {
      const authData = {
        user: {
          id: 0,
          username: 'admin',
          email: '<EMAIL>',
          userType: 'admin',
          role: 'admin',
          name: 'System Administrator'
        },
        token: generateSessionToken(),
        expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
      };

      localStorage.setItem(AUTH_KEY, JSON.stringify(authData));
      console.log('✅ Default admin login successful');
      return { success: true, user: authData.user };
    }

    // Check database for vendor/sub-admin users
    const passwordHash = await hashPassword(password);
    
    const users = await sql`
      SELECT
        u.id,
        u.username,
        u.email,
        u.user_type,
        u.user_id,
        u.is_active,
        u.must_change_password,
        u.last_login,
        CASE
          WHEN u.user_type = 'vendor' THEN v.company_name
          WHEN u.user_type = 'subadmin' THEN s.name
          ELSE u.username
        END as display_name
      FROM auth_users u
      LEFT JOIN vendors v ON u.user_type = 'vendor' AND u.user_id = v.id
      LEFT JOIN users s ON u.user_type = 'subadmin' AND u.user_id = s.id
      WHERE u.username = ${username}
        AND u.password_hash = ${passwordHash}
        AND u.is_active = true
    `;

    if (users.length === 0) {
      console.log('❌ Invalid credentials for username:', username);
      return { success: false, error: 'Invalid username or password' };
    }

    const user = users[0];

    // Update last login
    await sql`
      UPDATE auth_users
      SET last_login = CURRENT_TIMESTAMP
      WHERE id = ${user.id}
    `;

    // Create session
    const sessionToken = generateSessionToken();
    const expiresAt = new Date(Date.now() + (24 * 60 * 60 * 1000)); // 24 hours

    try {
      await sql`
        INSERT INTO auth_sessions (user_id, session_token, expires_at, user_agent)
        VALUES (${user.id}, ${sessionToken}, ${expiresAt}, ${navigator.userAgent || 'Unknown'})
      `;
    } catch (sessionError) {
      console.log('⚠️ Session creation failed, continuing without session tracking');
    }

    const authData = {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        userType: user.user_type,
        userId: user.user_id,
        role: user.user_type,
        name: user.display_name,
        mustChangePassword: user.must_change_password
      },
      token: sessionToken,
      expiresAt: expiresAt.getTime()
    };

    localStorage.setItem(AUTH_KEY, JSON.stringify(authData));
    
    console.log('✅ Login successful for:', user.username, 'Type:', user.user_type);
    return { 
      success: true, 
      user: authData.user,
      mustChangePassword: user.must_change_password
    };

  } catch (error) {
    console.error('❌ Login error:', error);
    return { success: false, error: 'Login failed: ' + error.message };
  }
};

/**
 * Logout function with enhanced security
 */
export const logout = async () => {
  try {
    const authData = getCurrentAuthData();

    if (authData && authData.token) {
      // Invalidate session in database
      try {
        await sql`
          UPDATE auth_sessions
          SET is_active = false
          WHERE session_token = ${authData.token}
        `;
      } catch (error) {
        console.log('⚠️ Session invalidation failed:', error);
      }
    }
  } catch (error) {
    console.error('Error during logout:', error);
  }

  // Clear all authentication data
  localStorage.removeItem(AUTH_KEY);
  sessionStorage.clear();

  // Clear browser history to prevent back button access
  if (window.history && window.history.pushState) {
    window.history.pushState(null, null, '/login');
    window.history.pushState(null, null, '/login');
    window.onpopstate = function () {
      window.history.go(1);
    };
  }

  // Force redirect to login
  window.location.replace('/login');
};

/**
 * Check if user is authenticated with enhanced validation
 */
export const isAuthenticated = () => {
  try {
    const authData = localStorage.getItem(AUTH_KEY);
    if (!authData) {
      console.log('🔍 No auth data found in localStorage');
      return false;
    }

    const parsed = JSON.parse(authData);

    // Check if token exists
    if (!parsed.token || !parsed.user || !parsed.expiresAt) {
      console.log('🔍 Invalid auth data structure');
      localStorage.removeItem(AUTH_KEY);
      return false;
    }

    // Check if token is expired
    if (parsed.expiresAt <= Date.now()) {
      console.log('🔍 Auth token expired');
      localStorage.removeItem(AUTH_KEY);
      return false;
    }

    console.log('🔍 User is authenticated');
    return true;
  } catch (error) {
    console.error('🔍 Error checking authentication:', error);
    localStorage.removeItem(AUTH_KEY);
    return false;
  }
};

/**
 * Get current user data
 */
export const getCurrentUser = () => {
  try {
    const authData = localStorage.getItem(AUTH_KEY);
    if (!authData) return null;

    const parsed = JSON.parse(authData);
    if (parsed.expiresAt > Date.now()) {
      return parsed.user;
    }
    return null;
  } catch {
    return null;
  }
};

/**
 * Get current auth data
 */
const getCurrentAuthData = () => {
  try {
    const authData = localStorage.getItem(AUTH_KEY);
    if (!authData) return null;

    const parsed = JSON.parse(authData);
    if (parsed.expiresAt > Date.now()) {
      return parsed;
    }
    return null;
  } catch {
    return null;
  }
};

/**
 * Change password
 */
export const changePassword = async (currentPassword, newPassword) => {
  try {
    const user = getCurrentUser();
    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Verify current password
    const currentHash = await hashPassword(currentPassword);
    const userCheck = await sql`
      SELECT id FROM auth_users
      WHERE id = ${user.id} AND password_hash = ${currentHash}
    `;

    if (userCheck.length === 0) {
      return { success: false, error: 'Current password is incorrect' };
    }

    // Update password
    const newHash = await hashPassword(newPassword);
    await sql`
      UPDATE auth_users
      SET password_hash = ${newHash}, must_change_password = false, updated_at = CURRENT_TIMESTAMP
      WHERE id = ${user.id}
    `;

    console.log('✅ Password changed successfully for user:', user.username);
    return { success: true, message: 'Password changed successfully' };

  } catch (error) {
    console.error('❌ Password change error:', error);
    return { success: false, error: 'Failed to change password: ' + error.message };
  }
};

/**
 * Check user permissions
 */
export const hasPermission = (requiredRole) => {
  const user = getCurrentUser();
  if (!user) return false;

  const roleHierarchy = {
    'admin': 3,
    'subadmin': 2,
    'vendor': 1
  };

  const userLevel = roleHierarchy[user.userType] || 0;
  const requiredLevel = roleHierarchy[requiredRole] || 0;

  return userLevel >= requiredLevel;
};

/**
 * Get user dashboard URL based on user type
 */
export const getUserDashboardUrl = (user) => {
  switch (user.userType) {
    case 'admin':
      return '/dashboard';
    case 'subadmin':
      return '/dashboard'; // Same dashboard but with restricted permissions
    case 'vendor':
      return '/vendor-dashboard'; // Vendor-specific dashboard
    default:
      return '/dashboard';
  }
};
